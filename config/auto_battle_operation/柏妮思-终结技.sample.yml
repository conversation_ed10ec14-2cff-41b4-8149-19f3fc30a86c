# 根据动画实测，动画持续5秒, 第三秒开始可以切人
operations:
  - op_name: "设置状态"
    state: "自定义-连携换人"
    seconds_add: -1
  - op_name: "设置状态"
    state: "自定义-动作不打断"
    seconds: 5
  - op_name: "按键-终结技"
    post_delay: 0.1
    repeat: 10
  - op_name: "等待秒数"
    seconds: 2
  - op_name: "设置状态"
    data: ["自定义-速切结束"]
  - op_name: "清除状态"
    state: "自定义-异常-火"
  - op_name: "等待秒数"
    seconds: 0.5
  - op_name: "设置状态"
    state: "自定义-快速支援换人"
  - op_name: "按键-切换角色-下一个"
  - op_name: "清除状态"
    state_list: ["自定义-速切结束", "自定义-动作不打断"]
  - op_name: "等待秒数"
    seconds: 1.5