# 自动锁定
handlers:
  # 优化后的处理程序 1：锁定丢失超过三秒，且未近距离锁定，重新锁定
  # 明确排除“目标-近距离锁定”的状态，确保只在没有目标时尝试重新锁定。
  - states: "[自定义-锁定丢失, 3, 99]"
    debug_name: "锁定丢失超过三秒，重新锁定"
    operations:
      - op_name: "按键-锁定敌人"
        way: "按下"
        press: 0.02
      - op_name: "清除状态"
        state: "自定义-锁定丢失"
      - op_name: "设置状态"
        state: "自定义-锁定冷却"

  # 处理程序 2 保持不变：发现锁定丢失三秒内又检测到了，不重新锁定
  # 此处理程序的作用是在检测到近距离目标时，及时清除“锁定丢失”状态，
  # 防止不必要的重新锁定操作，与优化后的处理程序1形成互补。
  - states: "[自定义-锁定丢失, 0, 3] & [目标-近距离锁定, 0, 1]"
    debug_name: "发现锁定丢失三秒内又检测到了，不重新锁定"
    operations:
      - op_name: "清除状态"
        state: "自定义-锁定丢失"

  # 处理程序 3 保持不变：发现锁定丢失
  # 此处理程序负责在没有目标、不在冷却、且未处于锁定丢失初始阶段时，设置“锁定丢失”状态。
  # 其逻辑独立且合理，无需修改。
  - states: "![目标-近距离锁定, 0, 1] & ![自定义-锁定冷却, 0, 1] & ![自定义-锁定丢失, 0, 3]"
    debug_name: "发现锁定丢失"
    operations:
      - op_name: "设置状态"
        state: "自定义-锁定丢失"
