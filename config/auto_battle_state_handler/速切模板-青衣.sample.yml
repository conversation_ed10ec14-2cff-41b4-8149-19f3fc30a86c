template_name: "速切模板-青衣"
handlers:
  - states: "[前台-青衣]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "青衣-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        sub_handlers:
          - state_template: "闪A模板-青衣"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 0.3
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[青衣-电压]{75, 101}"
        operations:
          - operation_template: "青衣-醉花云月转"

      - states: "[青衣-终结技可用] & [青衣-电压]{0,25}"
        operations:
          - operation_template: "青衣-终结技"

      - states: "[青衣-特殊技可用]"
        interrupt_states: "[青衣-电压]{75,100}"
        operations:
        - operation_template: "青衣-特殊技"
        - operation_template: "青衣-闪电鞭"

      - states: "[青衣-电压]{0, 75}"
        interrupt_states: "[青衣-电压]{75,100}"
        operations:
          - operation_template: "青衣-闪电鞭"