template_name: "速切模板-浮波柚叶"
handlers:
  - states: "[前台-浮波柚叶]"
    sub_handlers:
      # 支援
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "通用-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "浮波柚叶-闪A"

      # 连携
      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.3]"
            operations:
              - op_name: "等待秒数"
                seconds: 0.5
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      # 终结技时刻，需要注意出场第一秒可能识别错误
      - states: "[浮波柚叶-终结技可用] & ![自定义-连携换人, 0, 10]"
        operations:
          - operation_template: "浮波柚叶-终结技"

      - states: "[浮波柚叶-特殊技可用] & [浮波柚叶-能量]{ 60, 120}"
        operations:
          - operation_template: "浮波柚叶-强化特殊技合轴"

      - states: ""
        operations:
          - operation_template: "浮波柚叶-普攻合轴"