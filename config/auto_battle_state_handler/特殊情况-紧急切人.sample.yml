template_name: "特殊情况-紧急切人"
handlers:
  # 终结技连携结束之后要清空连携状态
  - states: "[自定义-耀嘉音-终结技连携, 0, 180]{0, 0} & [自定义-连携换人, 1, 10]"
    debug_name: "清空连携状态"
    operations:
      - op_name: "清除状态"
        state_list: ["自定义-耀嘉音-终结技连携", "自定义-连携换人"]

  # 嘉音放大招和补唱歌, 至少3分钟一次
  - states: "![前台-耀嘉音]"
    sub_handlers:
      - states: "[耀嘉音-终结技可用] & ![自定义-连携换人, 0, 10]"
        debug_name: "切耀嘉音放大招"
        operations:
          - op_name: "按键-切换角色"
            agent_name: "耀嘉音"
      - states: "[耀嘉音-能量]{0, 120} & ![前台-耀嘉音] & ![自定义-耀嘉音-唱歌, 0, 180]"
        debug_name: "耀嘉音补唱歌"
        operations:
          - op_name: "按键-切换角色"
            agent_name: "耀嘉音"

  # 青衣在失衡其间不小心切出来了就切到输出去
  - states: "[前台-青衣] & [自定义-连携换人, 0, 10]"
    debug_name: "青衣失衡离场"
    sub_handlers:
      - states: "[后台-2-强攻] | [后台-2-异常]"
        debug_name: "切换上位输出"
        operations:
          - op_name: "按键-切换角色-上一个"

      - states: "[后台-1-强攻] | [后台-1-异常]"
        debug_name: "切换下位输出"
        operations:
          - op_name: "按键-切换角色-下一个"

  # 青衣在失衡其间不小心切出来了就切到输出去
  - states: "[前台-击破] & [后台-仪玄] & [自定义-连携换人, 0, 10]"
    debug_name: "仪玄打输出了"
    operations:
      - op_name: "按键-切换角色"
        agent_name: "仪玄"

  # 特殊处理，注意这里要等一秒，这样每次切人之后优先至少做一次角色模板里的动作，防止循环切人
  # 没有比掉血更特殊的，掉血了就不要进来切人了
  - states: "![自定义-血量扣减, 0, 1] & !([按键-切换角色-下一个, 0, 1]|[按键-切换角色-上一个, 0, 1])"
    debug_name: "紧急切人检查"
    sub_handlers:
      - states: "![自定义-动作不打断, -10, 0]"
        debug_name: "不打断动作切人"
        sub_handlers:
          # 开局紧急补BUFF
          - states: "![前台-露西] & [露西-特殊技可用] & ![自定义-露西-加油, 0, 30]"
            debug_name: "露西补BUFF"
            operations:
              - op_name: "按键-切换角色"
                agent_name: "露西"
              - operation_template: "露西-高飞球合轴"

          # 开局紧急补BUFF
          - states: "![前台-苍角] & [苍角-特殊技可用] & ![自定义-苍角-展旗, 0, 30]"
            debug_name: "苍角展旗"
            operations:
              - op_name: "按键-切换角色"
                agent_name: "苍角"

          # 开局紧急补BUFF
          - states: "![前台-扳机] & [扳机-能量]{00, 120} & [扳机-绝意]{0,10}"
            operations:
              - op_name: "按键-切换角色"
                agent_name: "扳机"

          # 开局紧急补BUFF, 需要注意的是，丽娜的BUFF时间不同动作是不一样的, 所以得像无敌时间那样从负数开始倒数，超过0就代表失效
          - states: "![前台-丽娜] & [丽娜-能量]{60, 120} & ![自定义-丽娜-人偶, -30, 0]"
            debug_name: "丽娜召唤人偶"
            operations:
              - op_name: "设置状态"
                state: "自定义-丽娜-人偶"
                seconds: 14
              - op_name: "按键-切换角色"
                agent_name: "丽娜"
              - op_name: "按键-特殊攻击"
                post_delay: 0.1
                repeat: 4
              - operation_template: "通用-切人普通攻击"

          # 挨打了或者没盾了补盾
          - states: "![前台-凯撒] & [凯撒-特殊技可用] & ([自定义-血量扣减, 0, 3] | ![自定义-凯撒-护盾, 0, 30])"
            debug_name: "凯撒补盾"
            operations:
              - op_name: "按键-切换角色"
                agent_name: "凯撒"
              - op_name: "清除状态"
                state_list: ["自定义-血量扣减"]

          # 星见雅 有6豆就切过去
          - states: "[雅-落霜]{6, 6} & ![前台-雅] & ![前台-支援]"
            debug_name: "雅满豆切入"
            operations:
              - op_name: "按键-切换角色"
                agent_name: "雅"

          # 队伍里有青衣，切过去打击破
          - states: "[青衣-能量]{0, 120} & ![自定义-连携换人, 0, 10] & ![自定义-黄光切人, 0, 2.5] & ![前台-青衣]"
            debug_name: "青衣击破准备"
            operations:
              - op_name: "按键-切换角色"
                agent_name: "青衣"

          - states: "([妮可-特殊技可用] | [妮可-终结技可用])  & ![自定义-妮可-能量场, -15, 3.5] & ![前台-妮可]"
            debug_name: "妮可能量场检查"
            sub_handlers:
              # 妮可失衡期间切到妮可，这里的-15，3.5意思是妮可BUFF是倒数制
              # 因为不同动作持续时间不一样。其实所有BUFF都应该这么写，但是之前写好的就懒得动了
              - states: "[自定义-连携换人, 5, 10]"
                debug_name: "失衡期补能量场"
                operations:
                  - op_name: "按键-切换角色"
                    agent_name: "妮可"

          # 双异常打紊乱，防止异常连续叠加
          - states: "[前台-异常]"
            debug_name: "异常紊乱检查"
            sub_handlers:
              # 防止打了太多的异常效果
              - states: "[后台-1-异常] | [后台-2-异常]"
                debug_name: "后台异常检查"
                sub_handlers:
                  # 如果打出了超过1.5管异常条，就切另外一个异常，300为一管异常条
                  - states: "
                      [自定义-异常-冰, 0, 999]{450, 9999} |
                      [自定义-异常-物理, 0, 999]{450, 9999} |
                      [自定义-异常-烈霜, 0, 999]{450, 9999} |
                      [自定义-异常-火, 0, 999]{450, 9999} |
                      [自定义-异常-电, 0, 999]{450, 9999}"
                    debug_name: "异常条溢出"
                    sub_handlers:
                      - states: "[后台-1-异常]"
                        debug_name: "切换异常角色"
                        operations:
                          - op_name: "按键-切换角色-下一个"
                      - states: "[后台-2-异常]"
                        debug_name: "切换异常角色"
                        operations:
                          - op_name: "按键-切换角色-上一个"

          - states: "[前台-击破] & ![自定义-速切结束, 0, 30] & [自定义-连携换人, 0, 10]"
            debug_name: "击破连携检查"
            sub_handlers:
              - states: "[后台-1-强攻] | [后台-2-强攻] | [后台-1-异常] | [后台-2-异常]"
                debug_name: "击破后切输出"
                operations:
                  - op_name: "按键-特殊攻击"
                    post_delay: 0.2
                    repeat: 1
                  - op_name: "设置状态"
                    data: ["自定义-速切结束"]
                  - op_name: "按键-特殊攻击"
                    post_delay: 0.2
                    repeat: 1
      - states: ""  # 可以打断动作的
        sub_handlers:
          - states: "([妮可-特殊技可用] | [妮可-终结技可用])  & ![自定义-妮可-能量场, -15, 3.5] & ![前台-妮可]"
            debug_name: "妮可能量场准备"
            sub_handlers:
              - states: "[前台-零号安比] & [自定义-零号安比-白雷, 0, 99]{300, 999}"  # 零号安比爆发前补状态
                operations:
                  - op_name: "按键-切换角色"
                    agent_name: "妮可"