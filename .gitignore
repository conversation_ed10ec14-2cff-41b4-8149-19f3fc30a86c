# 开发过程产生的临时文件
build/
dist/
.idea/
.vscode/
__pycache__/
deploy/build/
deploy/dist/
.env
recording.jsonl
QWEN.md
GEMINI.md
CLAUDE.md
.lingma/
.qwen/

# 子模块
test/
zzz-od-test/

# 用户独有的配置文件
env.bat
config/custom.yml
config/env.yml
config/game.yml
config/dodge_assistant.yml
config/zzz_one_dragon.yml
config/one_dragon.yml
config/[0-9]*/
config/dodge/*
!config/dodge/*.sample.yml
config/auto_battle/*
!config/auto_battle/*.sample.yml
config/auto_battle_state_handler/*
!config/auto_battle_state_handler/*.sample.yml
config/auto_battle_operation/*
!config/auto_battle_operation/*.sample.yml
config/hollow_zero_challenge/*
!config/hollow_zero_challenge/*.sample.yml
config/lost_void_challenge/*
!config/lost_void_challenge/*.sample.yml
config/yolo.yml
config/model.yml
custom/assets/ui/banner
assets/ui/version_poster.webp
assets/ui/remote_banner.webp
config/world_patrol_route_list/

# 执行文件
*.exe

# 用户运行时产生的文件
.log/
.install/
.temp_clone/
.temp_clone**/
.debug/
.conda/
.venv/
notice_cache/

# 需自行下载的模型文件
models/

# telemetry
config/telemetry.yml
config/privacy.yml

telemetry_debug.log
telemetry_*.log

telemetry_temp/
telemetry_cache/

src/zzz_od/telemetry/__pycache__/
src/zzz_od/telemetry/**/__pycache__/

telemetry_export_*.json
telemetry_report_*.json
privacy_report_*.json
