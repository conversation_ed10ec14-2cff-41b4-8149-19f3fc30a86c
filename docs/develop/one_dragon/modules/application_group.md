# 应用任务组(ApplicationGroup)设计文档

## 1. 项目概述

本文档描述了将当前的OneDragonApp升级为支持多任务组模式的设计方案。新的架构将允许用户创建多个任务组，每个任务组可以包含多个Application，每个Application都可以有独立的配置。

## 2. 当前架构分析

### 2.1 现有架构概述

当前项目采用三层架构：

```mermaid
graph TD
    A["OneDragonApp<br/>一条龙应用"] --> B["Application<br/>具体应用"]
    B --> C["Operation<br/>操作节点"]

    D["OneDragonConfig<br/>全局配置"] --> E["OneDragonAppConfig<br/>应用配置"]
    E --> F["各应用独立配置<br/>(CoffeeConfig等)"]

    G["OneDragonInstance<br/>实例管理"] --> H["多账号支持"]
```

### 2.2 核心组件分析

#### 2.2.1 OneDragonApp
- **职责**: 管理应用执行顺序和实例切换
- **核心功能**:
  - 获取应用列表(`get_app_list()`)
  - 按顺序执行应用(`get_one_dragon_apps_in_order()`)
  - 多实例支持和账号切换
- **限制**: 只支持单一的应用执行序列

#### 2.2.2 Application
- **职责**: 封装具体的业务逻辑
- **核心属性**:
  - `app_id`: 应用唯一标识
  - `run_record`: 运行记录
  - `need_notify`: 通知配置
- **配置**: 每个应用有独立的配置类

#### 2.2.3 配置系统
- **层次结构**:
  - 全局配置(`OneDragonConfig`)
  - 应用配置(`OneDragonAppConfig`)
  - 具体应用配置(如`CoffeeConfig`)
- **实例支持**: 通过`instance_idx`支持多实例配置

## 3. 新架构设计

### 3.1 整体架构

```mermaid
graph TD
    A["OneDragonApp<br/>升级版"] --> B["ApplicationGroupManager<br/>任务组管理器"]
    B --> C["ApplicationGroup<br/>任务组"]
    C --> D["GroupApplication<br/>组内应用"]
    D --> E["Operation<br/>操作节点"]

    F["ApplicationGroupConfig<br/>任务组配置"] --> G["GroupApplicationConfig<br/>组内应用配置"]
    G --> H["具体应用配置<br/>(CoffeeConfig等)"]

    I["OneDragonInstance<br/>实例管理"] --> J["多账号支持"]
```

### 3.2 核心类设计

#### 3.2.1 ApplicationGroup (任务组)

```python
class ApplicationGroup:
    """应用任务组"""

    def __init__(self, group_id: str, group_name: str, ctx: OneDragonContext):
        self.group_id: str = group_id
        self.group_name: str = group_name
        self.ctx: OneDragonContext = ctx
        self.applications: list[GroupApplication] = []
        self.config: ApplicationGroupConfig = ApplicationGroupConfig(group_id, ctx.current_instance_idx)
        self.enabled: bool = True
        self.execution_order: int = 0

    def add_application(self, app: GroupApplication) -> None:
        """添加应用到任务组"""

    def remove_application(self, app_id: str) -> None:
        """从任务组移除应用"""

    def execute(self) -> OperationResult:
        """执行任务组中的所有应用"""

    def get_execution_status(self) -> GroupExecutionStatus:
        """获取任务组执行状态"""
```

#### 3.2.2 GroupApplication (组内应用)

```python
class GroupApplication:
    """任务组内的应用包装器"""

    def __init__(self, app: Application, group_id: str):
        self.app: Application = app
        self.group_id: str = group_id
        self.enabled_in_group: bool = True
        self.execution_order: int = 0
        self.group_config: GroupApplicationConfig = None

    def execute_in_group(self) -> OperationResult:
        """在任务组上下文中执行应用"""

    def get_group_specific_config(self) -> dict:
        """获取在此任务组中的特定配置"""
```

#### 3.2.3 ApplicationGroupManager (任务组管理器)

```python
class ApplicationGroupManager:
    """应用任务组管理器"""

    def __init__(self, ctx: OneDragonContext):
        self.ctx: OneDragonContext = ctx
        self.groups: dict[str, ApplicationGroup] = {}
        self.config: ApplicationGroupManagerConfig = ApplicationGroupManagerConfig(ctx.current_instance_idx)

    def create_group(self, group_name: str) -> ApplicationGroup:
        """创建新的任务组"""

    def delete_group(self, group_id: str) -> bool:
        """删除任务组"""

    def get_enabled_groups(self) -> list[ApplicationGroup]:
        """获取启用的任务组列表"""

    def execute_all_groups(self) -> dict[str, OperationResult]:
        """执行所有启用的任务组"""
```

## 4. 配置系统设计

### 4.1 配置层次结构

```mermaid
graph TD
    A["ApplicationGroupManagerConfig<br/>任务组管理器配置"] --> B["ApplicationGroupConfig<br/>任务组配置"]
    B --> C["GroupApplicationConfig<br/>组内应用配置"]
    C --> D["具体应用配置<br/>(CoffeeConfig等)"]

    E["OneDragonInstance<br/>实例"] --> A
    E --> B
    E --> C
    E --> D
```

### 4.2 配置类设计

#### 4.2.1 ApplicationGroupManagerConfig

```python
class ApplicationGroupManagerConfig(YamlConfig):
    """任务组管理器配置"""

    def __init__(self, instance_idx: Optional[int] = None):
        YamlConfig.__init__(self, 'application_group_manager', instance_idx=instance_idx)

    @property
    def enabled_groups(self) -> list[str]:
        """启用的任务组ID列表"""
        return self.get("enabled_groups", [])

    @property
    def group_execution_order(self) -> list[str]:
        """任务组执行顺序"""
        return self.get("group_execution_order", [])

    @property
    def concurrent_execution(self) -> bool:
        """是否支持并发执行任务组"""
        return self.get("concurrent_execution", False)
```

#### 4.2.2 ApplicationGroupConfig

```python
class ApplicationGroupConfig(YamlConfig):
    """任务组配置"""

    def __init__(self, group_id: str, instance_idx: Optional[int] = None):
        YamlConfig.__init__(self, f'group_{group_id}', instance_idx=instance_idx, sub_dir=['application_groups'])
        self.group_id = group_id

    @property
    def group_name(self) -> str:
        """任务组名称"""
        return self.get("group_name", f"任务组_{self.group_id}")

    @property
    def enabled_applications(self) -> list[str]:
        """启用的应用ID列表"""
        return self.get("enabled_applications", [])

    @property
    def application_execution_order(self) -> list[str]:
        """应用执行顺序"""
        return self.get("application_execution_order", [])

    @property
    def retry_failed_applications(self) -> bool:
        """是否重试失败的应用"""
        return self.get("retry_failed_applications", True)

    @property
    def stop_on_first_failure(self) -> bool:
        """首次失败时是否停止执行"""
        return self.get("stop_on_first_failure", False)
```

#### 4.2.3 GroupApplicationConfig

```python
class GroupApplicationConfig(YamlConfig):
    """组内应用配置"""

    def __init__(self, group_id: str, app_id: str, instance_idx: Optional[int] = None):
        YamlConfig.__init__(self, f'{app_id}_in_{group_id}',
                           instance_idx=instance_idx,
                           sub_dir=['application_groups', group_id])
        self.group_id = group_id
        self.app_id = app_id

    @property
    def enabled(self) -> bool:
        """在此任务组中是否启用"""
        return self.get("enabled", True)

    @property
    def custom_config_overrides(self) -> dict:
        """自定义配置覆盖"""
        return self.get("custom_config_overrides", {})

    @property
    def timeout_seconds(self) -> float:
        """超时时间"""
        return self.get("timeout_seconds", -1)

    @property
    def retry_times(self) -> int:
        """重试次数"""
        return self.get("retry_times", 1)
```

## 5. 执行引擎设计

### 5.1 执行流程

```mermaid
graph TD
    A["开始执行"] --> B["加载任务组管理器配置"]
    B --> C["获取启用的任务组列表"]
    C --> D["按顺序执行任务组"]
    D --> E["加载任务组配置"]
    E --> F["获取任务组内启用的应用"]
    F --> G["按顺序执行应用"]
    G --> H["应用执行完成"]
    H --> I{"是否有更多应用?"}
    I -->|是| G
    I -->|否| J["任务组执行完成"]
    J --> K{"是否有更多任务组?"}
    K -->|是| D
    K -->|否| L["全部执行完成"]
```

### 5.2 状态管理

#### 5.2.1 执行状态枚举

```python
class GroupExecutionStatus(Enum):
    """任务组执行状态"""
    WAITING = "等待执行"
    RUNNING = "正在执行"
    SUCCESS = "执行成功"
    FAILED = "执行失败"
    CANCELLED = "已取消"
    PAUSED = "已暂停"

class ApplicationExecutionStatus(Enum):
    """应用执行状态"""
    WAITING = "等待执行"
    RUNNING = "正在执行"
    SUCCESS = "执行成功"
    FAILED = "执行失败"
    SKIPPED = "已跳过"
```

#### 5.2.2 执行记录

```python
class GroupExecutionRecord:
    """任务组执行记录"""

    def __init__(self, group_id: str):
        self.group_id: str = group_id
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.status: GroupExecutionStatus = GroupExecutionStatus.WAITING
        self.application_records: dict[str, ApplicationExecutionRecord] = {}
        self.error_message: Optional[str] = None

    def start_execution(self) -> None:
        """开始执行"""

    def complete_execution(self, success: bool, error_message: str = None) -> None:
        """完成执行"""

    def get_execution_summary(self) -> dict:
        """获取执行摘要"""
```

## 6. 向后兼容性

### 6.1 兼容性策略

为确保现有代码的正常运行，新架构将提供以下兼容性支持：

1. **默认任务组**: 自动创建一个"默认任务组"，包含所有现有应用
2. **配置迁移**: 自动将现有的`OneDragonAppConfig`迁移到新的配置结构
3. **API兼容**: 保持现有的`OneDragonApp`接口不变，内部使用新的任务组系统

### 6.2 迁移流程

```mermaid
graph TD
    A["检测现有配置"] --> B["创建默认任务组"]
    B --> C["迁移应用配置"]
    C --> D["更新配置文件"]
    D --> E["验证迁移结果"]
    E --> F["完成迁移"]
```

## 8. 配置文件示例

### 8.1 任务组管理器配置示例

```yaml
# config/01/application_group_manager.yml
enabled_groups:
  - "daily_tasks"
  - "weekly_tasks"
  - "special_events"

group_execution_order:
  - "daily_tasks"
  - "weekly_tasks"
  - "special_events"

concurrent_execution: false
```

### 8.2 任务组配置示例

```yaml
# config/01/application_groups/group_daily_tasks.yml
group_name: "日常任务"
enabled_applications:
  - "email"
  - "coffee"
  - "scratch_card"

application_execution_order:
  - "email"
  - "coffee"
  - "scratch_card"

retry_failed_applications: true
stop_on_first_failure: false
```

### 8.3 组内应用配置示例

```yaml
# config/01/application_groups/daily_tasks/coffee_in_daily_tasks.yml
enabled: true
timeout_seconds: 300
retry_times: 2

custom_config_overrides:
  choose_way: "plan_priority"
  challenge_way: "all"
  card_num: "1"
```

## 9. 实现路径

### 9.1 开发阶段

#### 阶段1: 核心架构实现
- [ ] 实现`ApplicationGroup`类
- [ ] 实现`GroupApplication`类
- [ ] 实现`ApplicationGroupManager`类
- [ ] 实现配置系统(`ApplicationGroupConfig`等)

#### 阶段2: 执行引擎实现
- [ ] 实现任务组执行逻辑
- [ ] 实现状态管理系统
- [ ] 实现错误处理和重试机制
- [ ] 实现执行记录和日志

#### 阶段3: 向后兼容性
- [ ] 实现配置迁移工具
- [ ] 实现默认任务组创建
- [ ] 确保现有API兼容性
- [ ] 编写迁移测试用例

#### 阶段4: UI界面实现
- [ ] 设计任务组管理界面
- [ ] 实现任务组创建/编辑功能
- [ ] 实现执行状态监控界面
- [ ] 实现应用配置界面

#### 阶段5: 测试和优化
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 性能优化
- [ ] 用户体验优化

### 9.2 关键技术点

#### 9.2.1 配置覆盖机制
实现应用在不同任务组中的配置覆盖：

```python
def get_effective_config(self, app_id: str, group_id: str) -> dict:
    """获取应用在特定任务组中的有效配置"""
    base_config = self.get_app_base_config(app_id)
    group_config = self.get_group_app_config(group_id, app_id)

    # 合并配置，组配置覆盖基础配置
    effective_config = {**base_config, **group_config.custom_config_overrides}
    return effective_config
```

#### 9.2.2 动态应用实例化
支持应用在不同任务组中的独立实例化：

```python
def create_app_instance(self, app_class: type, group_id: str) -> Application:
    """为特定任务组创建应用实例"""
    app = app_class(self.ctx)

    # 应用组特定的配置
    group_config = self.get_group_app_config(group_id, app.app_id)
    if group_config.custom_config_overrides:
        app.apply_config_overrides(group_config.custom_config_overrides)

    return app
```

#### 9.2.3 状态同步机制
确保UI界面能实时反映执行状态：

```python
class ExecutionStatusManager:
    """执行状态管理器"""

    def __init__(self):
        self.status_listeners: list[Callable] = []
        self.current_status: dict = {}

    def update_status(self, group_id: str, app_id: str, status: str) -> None:
        """更新执行状态"""
        self.current_status[f"{group_id}:{app_id}"] = status
        self.notify_listeners(group_id, app_id, status)

    def notify_listeners(self, group_id: str, app_id: str, status: str) -> None:
        """通知状态监听器"""
        for listener in self.status_listeners:
            listener(group_id, app_id, status)
```

## 10. 总结

### 10.1 架构优势

新的应用任务组架构具有以下优势：

1. **灵活性**: 用户可以根据需要创建多个任务组，每个任务组包含不同的应用组合
2. **可配置性**: 每个应用在不同任务组中可以有不同的配置，满足多样化需求
3. **可扩展性**: 架构设计支持未来功能的扩展，如并发执行、条件执行等
4. **向后兼容**: 现有代码无需修改即可正常运行
5. **用户友好**: 提供直观的UI界面，降低使用门槛

### 10.2 应用场景

该架构特别适用于以下场景：

1. **多账号管理**: 不同账号需要执行不同的应用组合
2. **时间段任务**: 不同时间段执行不同的任务组
3. **条件执行**: 根据游戏状态或外部条件选择执行不同的任务组
4. **测试环境**: 为测试创建专门的任务组，避免影响正常使用

### 10.3 未来扩展

基于这个架构，未来可以考虑以下扩展：

1. **条件执行**: 支持基于条件的任务组执行
2. **并发执行**: 支持多个任务组并发执行
3. **定时执行**: 支持任务组的定时执行
4. **远程管理**: 支持通过网络接口管理任务组
5. **模板系统**: 支持任务组模板的导入导出

这个设计为游戏自动化项目提供了一个强大而灵活的任务管理框架，将大大提升用户体验和系统的可维护性。
```