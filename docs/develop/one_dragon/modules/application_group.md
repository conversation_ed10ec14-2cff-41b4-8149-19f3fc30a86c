# 应用组(ApplicationGroup)设计文档

## 1. 项目概述

本文档描述了将当前的OneDragonApp升级为支持多应用组模式的设计方案。新的架构将允许用户创建多个应用组，每个应用组可以包含多个Application，每个Application在不同应用组中可以有独立的配置。

## 2. 设计方案概述

### 2.1 核心设计理念

基于以下8个核心要点的改造方案：

1. **应用注册机制**: `OneDragonContext` 新增应用注册方法，可以注册应用的创建函数
2. **应用组执行器**: 新增 `OneDragonGroupApp`，类似于当前的 `OneDragonApp`，用于执行一个应用组
3. **应用组配置**: `OneDragonGroupApp` 接受一个 `OneDragonGroupConfig`，包含具体的应用ID列表
4. **动态应用创建**: `OneDragonGroupApp` 按任务ID从 `OneDragonContext` 创建应用并执行
5. **向后兼容**: 当前 `OneDragonApp` 改造为 `group_id=one_dragon` 的默认应用组
6. **分层配置**: 应用配置按 `instance_idx` 和 `group_id` 独立，支持配置继承
7. **统一运行记录**: 运行记录保持当前结构，按账号独立，不区分应用组
8. **应用组管理**: `OneDragonAppGroupManager` 管理应用组的增删改查

### 2.2 架构对比

#### 2.2.1 当前架构
```mermaid
graph TD
    A["OneDragonApp"] --> B["固定应用列表"]
    B --> C["Application实例"]
    C --> D["应用配置"]
    C --> E["运行记录"]
```

#### 2.2.2 新架构
```mermaid
graph TD
    A["OneDragonAppGroupManager"] --> B["OneDragonGroupApp"]
    B --> C["OneDragonGroupConfig"]
    C --> D["应用ID列表"]
    B --> E["OneDragonContext"]
    E --> F["应用注册表"]
    F --> G["动态创建Application"]
    G --> H["应用组内配置"]
    G --> I["账号运行记录"]
```

## 3. 核心组件设计

### 3.2 核心类设计

#### 3.2.1 OneDragonContext 增强

```python
class OneDragonContext:
    """增强的上下文类，支持应用注册和运行记录注册"""

    def __init__(self, controller: Optional[ControllerBase] = None):
        # ... 现有初始化代码 ...
        self.app_registry: AppRegistry = AppRegistry()
        self.run_record_registry: RunRecordRegistry = RunRecordRegistry()

    def register_app(self, app_id: str, app_factory: Callable[[OneDragonContext], Application]) -> None:
        """注册应用创建函数

        Args:
            app_id: 应用唯一标识
            app_factory: 应用创建函数，接受OneDragonContext参数
        """
        pass

    def create_app(self, app_id: str) -> Optional[Application]:
        """根据应用ID创建应用实例

        Args:
            app_id: 应用ID

        Returns:
            Application实例，如果应用ID不存在则返回None
        """
        pass

    def register_run_record(self, app_id: str, run_record_factory: Callable[[int], AppRunRecord]) -> None:
        """注册运行记录创建函数

        Args:
            app_id: 应用ID
            run_record_factory: 运行记录创建函数，接受instance_idx参数
        """
        pass

    def get_run_record(self, app_id: str) -> Optional[AppRunRecord]:
        """获取应用的运行记录

        Args:
            app_id: 应用ID

        Returns:
            运行记录实例，如果不存在则返回None
        """
        pass

    def get_registered_app_ids(self) -> list[str]:
        """获取所有已注册的应用ID"""
        pass
```

#### 3.2.2 AppRegistry (应用注册表)

```python
class AppRegistry:
    """应用注册表，管理应用的创建函数"""

    def __init__(self):
        self._app_factories: dict[str, Callable[[OneDragonContext], Application]] = {}

    def register(self, app_id: str, app_factory: Callable[[OneDragonContext], Application]) -> None:
        """注册应用创建函数

        Args:
            app_id: 应用唯一标识
            app_factory: 应用创建函数，接受OneDragonContext参数
        """
        pass

    def create_app(self, app_id: str, ctx: OneDragonContext) -> Optional[Application]:
        """创建应用实例

        Args:
            app_id: 应用ID
            ctx: 上下文

        Returns:
            Application实例，如果应用ID不存在则返回None
        """
        pass

    def get_app_ids(self) -> list[str]:
        """获取所有已注册的应用ID"""
        pass

    def is_registered(self, app_id: str) -> bool:
        """检查应用是否已注册"""
        pass
```

#### 3.2.3 RunRecordRegistry (运行记录注册表)

```python
class RunRecordRegistry:
    """运行记录注册表，管理运行记录的创建函数"""

    def __init__(self):
        self._run_record_factories: dict[str, Callable[[int], AppRunRecord]] = {}
        self._run_records: dict[str, AppRunRecord] = {}  # 缓存已创建的运行记录

    def register(self, app_id: str, run_record_factory: Callable[[int], AppRunRecord]) -> None:
        """注册运行记录创建函数

        Args:
            app_id: 应用ID
            run_record_factory: 运行记录创建函数，接受instance_idx参数
        """
        pass

    def get_run_record(self, app_id: str, instance_idx: int) -> Optional[AppRunRecord]:
        """获取运行记录实例

        Args:
            app_id: 应用ID
            instance_idx: 实例索引

        Returns:
            运行记录实例，如果不存在则返回None
        """
        pass

    def is_registered(self, app_id: str) -> bool:
        """检查运行记录是否已注册"""
        pass
```

#### 3.2.4 OneDragonGroupApp (应用组执行器)

```python
class OneDragonGroupApp:
    """应用组执行器，专门负责应用组的执行"""

    def __init__(self, ctx: OneDragonContext, group_config: OneDragonGroupConfig):
        """初始化应用组执行器

        Args:
            ctx: 上下文对象
            group_config: 应用组配置
        """
        self.ctx = ctx
        self.group_config = group_config

    def execute(self) -> OperationResult:
        """执行应用组中的所有应用

        Returns:
            执行结果
        """
        pass

    def get_app_list(self) -> list[str]:
        """获取应用组中的应用ID列表

        Returns:
            应用ID列表
        """
        pass

    def _should_run_app(self, app_id: str) -> bool:
        """判断应用是否应该执行

        Args:
            app_id: 应用ID

        Returns:
            是否应该执行
        """
        pass

    def _create_app_with_config(self, app_id: str) -> Optional[Application]:
        """创建应用实例并设置配置

        Args:
            app_id: 应用ID

        Returns:
            配置好的应用实例
        """
        pass
```

#### 3.2.4 OneDragonAppGroupManager (应用组管理器)

```python
class OneDragonAppGroupManager:
    """应用组管理器，专门负责管理OneDragonGroupConfig的增删改查"""

    BUILTIN_GROUP_ID = "one_dragon"  # 内置应用组ID

    def __init__(self, instance_idx: Optional[int] = None):
        """初始化应用组管理器

        Args:
            instance_idx: 实例索引
        """
        self.instance_idx = instance_idx
        self._groups: dict[str, OneDragonGroupConfig] = {}
        self._load_all_groups()

    def create_group(self, group_id: str, app_ids: list[str] = None) -> OneDragonGroupConfig:
        """创建新的应用组配置

        Args:
            group_id: 应用组ID
            app_ids: 应用ID列表

        Returns:
            创建的应用组配置
        """
        pass

    def delete_group(self, group_id: str) -> bool:
        """删除应用组配置

        Args:
            group_id: 应用组ID

        Returns:
            是否删除成功
        """
        pass

    def get_group(self, group_id: str) -> Optional[OneDragonGroupConfig]:
        """获取应用组配置"""
        pass

    def get_all_groups(self) -> dict[str, OneDragonGroupConfig]:
        """获取所有应用组配置"""
        pass

    def update_group(self, group_id: str, **kwargs) -> bool:
        """更新应用组配置"""
        pass

    def _load_all_groups(self):
        """加载所有应用组配置"""
        pass
```

## 4. 配置系统设计

### 4.1 配置层次结构

```mermaid
graph TD
    A["OneDragonGroupConfig<br/>应用组配置"] --> B["应用ID列表"]

    C["应用配置<br/>(CoffeeConfig等)"] --> D["默认配置<br/>group_id=one_dragon"]
    C --> E["应用组特定配置<br/>group_id=custom"]

    F["OneDragonInstance<br/>实例"] --> A
    F --> C

    G["配置继承关系"] --> H["group_id=one_dragon<br/>作为默认配置"]
    H --> I["其他group_id<br/>继承默认配置"]
```

### 4.2 核心配置类

#### 4.2.1 OneDragonGroupConfig (应用组配置)

```python
class OneDragonGroupConfig(YamlConfig):
    """应用组配置，配置文件路径: config/{instance_idx}/{group_id}/group.yml"""

    def __init__(self, group_id: str, instance_idx: Optional[int] = None):
        # 配置文件路径: config/{instance_idx}/{group_id}/group.yml
        YamlConfig.__init__(self, 'group', instance_idx=instance_idx, sub_dir=[group_id])
        self.group_id = group_id

    @property
    def app_ids(self) -> list[str]:
        """应用ID列表"""
        return self.get("app_ids", [])

    @app_ids.setter
    def app_ids(self, value: list[str]):
        self.update("app_ids", value)

    @property
    def use_group_config(self) -> list[str]:
        """使用应用组独立配置的应用ID列表

        这些应用将从 config/{instance_idx}/{group_id}/{app_id}.yml 加载配置
        """
        return self.get("use_group_config", [])

    @use_group_config.setter
    def use_group_config(self, value: list[str]):
        self.update("use_group_config", value)

    def add_app(self, app_id: str) -> None:
        """添加应用到应用组"""
        pass

    def remove_app(self, app_id: str) -> None:
        """从应用组移除应用"""
        pass

    def move_app(self, app_id: str, new_index: int) -> bool:
        """移动应用在应用组中的位置"""
        pass

    def should_use_group_config(self, app_id: str) -> bool:
        """检查应用是否应该使用应用组独立配置

        Args:
            app_id: 应用ID

        Returns:
            是否使用应用组独立配置
        """
        pass
```

### 4.2 配置文件组织结构

配置文件按照 `config/{instance_idx}/{group_id}/` 的结构组织：

```
config/
├── 01/                              # 实例01
│   ├── one_dragon/                  # 默认应用组 (group_id=one_dragon)
│   │   ├── group.yml               # 应用组配置
│   │   ├── coffee.yml              # 咖啡应用配置 (如果在use_group_config中)
│   │   └── email.yml               # 邮件应用配置 (如果在use_group_config中)
│   ├── daily_tasks/                # 日常应用组
│   │   ├── group.yml               # 应用组配置
│   │   ├── coffee.yml              # 咖啡应用在日常应用组中的配置
│   │   └── email.yml               # 邮件应用在日常应用组中的配置
│   ├── farming/                    # 体力消耗应用组
│   │   ├── group.yml               # 应用组配置
│   │   └── coffee.yml              # 咖啡应用在体力消耗组中的配置
│   ├── coffee.yml                  # 咖啡应用的全局默认配置
│   ├── email.yml                   # 邮件应用的全局默认配置
│   └── ...
└── 02/                             # 实例02
    └── ...
```

#### 4.2.1 配置加载逻辑

应用配置的加载优先级：
1. 如果应用在应用组的 `use_group_config` 列表中，加载 `config/{instance_idx}/{group_id}/{app_id}.yml`
2. 否则加载全局默认配置 `config/{instance_idx}/{app_id}.yml`

## 5. OneDragonApp的改造

### 5.1 OneDragonApp改造方案

```python
class OneDragonApp:
    """改造后的OneDragonApp，按账号执行group_id='one_dragon'的应用组"""

    def __init__(self, ctx: OneDragonContext):
        """初始化OneDragonApp

        Args:
            ctx: 上下文对象
        """
        self.ctx = ctx
        self.group_manager = OneDragonAppGroupManager(ctx.current_instance_idx)

    def execute(self) -> OperationResult:
        """执行默认应用组

        Returns:
            执行结果
        """
        pass

    def get_app_list(self) -> list[str]:
        """获取应用列表（保持兼容性）

        Returns:
            应用ID列表
        """
        pass

    def get_one_dragon_apps_in_order(self) -> list[str]:
        """获取按顺序排列的应用列表（保持兼容性）

        Returns:
            应用ID列表
        """
        pass

    def _ensure_default_group_exists(self):
        """确保默认应用组存在"""
        pass

    def _migrate_from_old_config(self):
        """从旧配置迁移到新配置"""
        pass
```
```

## 6. 应用注册机制

### 6.1 应用注册示例

```python
def register_all_apps(ctx: OneDragonContext):
    """注册所有应用到上下文"""

    # 注册咖啡应用
    ctx.register_app("coffee", lambda ctx: CoffeeApp(ctx))

    # 注册邮件应用
    ctx.register_app("email", lambda ctx: EmailApp(ctx))

    # 注册体力应用
    ctx.register_app("charge_plan", lambda ctx: ChargePlanApp(ctx))

    # 注册运行记录
    ctx.register_run_record("coffee", lambda instance_idx: AppRunRecord("coffee", instance_idx=instance_idx))
    ctx.register_run_record("email", lambda instance_idx: AppRunRecord("email", instance_idx=instance_idx))
    ctx.register_run_record("charge_plan", lambda instance_idx: AppRunRecord("charge_plan", instance_idx=instance_idx))
```

## 7. 运行记录系统

### 7.1 运行记录保持现有结构

根据方案要求，运行记录保持当前结构不变，即运行记录是账号下独立，不区分应用组。这意味着：

1. **统一性**: 同一个应用在所有应用组中共享同一个运行记录
2. **避免重复**: 如果应用在一个应用组中已经执行成功，在其他应用组中会被跳过
3. **简化管理**: 不需要为每个应用组维护独立的运行记录
4. **通过app_id注册**: 运行记录通过app_id在上下文中注册，应用可以获取创建
```

## 8. 使用示例

### 8.1 完整的使用流程

```python
def main():
    """主程序示例"""
    # 1. 创建上下文
    ctx = OneDragonContext()

    # 2. 注册所有应用
    register_all_apps(ctx)

    # 3. 创建应用组管理器
    group_manager = OneDragonAppGroupManager(ctx.current_instance_idx)

    # 4. 创建自定义应用组
    daily_group = group_manager.create_group("daily_tasks", ["email", "coffee", "scratch_card"])
    farming_group = group_manager.create_group("farming", ["charge_plan", "coffee"])

    # 5. 执行应用组
    daily_config = group_manager.get_group("daily_tasks")
    daily_app = OneDragonGroupApp(ctx, daily_config)
    result = daily_app.execute()

    farming_config = group_manager.get_group("farming")
    farming_app = OneDragonGroupApp(ctx, farming_config)
    result = farming_app.execute()
```

## 9. 向后兼容性

### 9.1 配置迁移

```python
class ConfigMigration:
    """配置迁移工具"""

    def __init__(self, instance_idx: Optional[int] = None):
        self.instance_idx = instance_idx

    def migrate_from_old_config(self):
        """从旧配置迁移到新配置"""
        pass

    def check_migration_needed(self) -> bool:
        """检查是否需要迁移"""
        pass
```

## 10. 实现路径

### 10.1 开发阶段

#### 阶段1: 核心架构实现 (1-2周)
- [ ] 实现`AppRegistry`和`RunRecordRegistry`注册表
- [ ] 增强`OneDragonContext`，添加应用和运行记录注册功能
- [ ] 实现`OneDragonGroupConfig`应用组配置
- [ ] 实现`OneDragonGroupApp`应用组执行器
- [ ] 实现`OneDragonAppGroupManager`应用组管理器

#### 阶段2: 配置系统实现 (1周)
- [ ] 实现配置文件组织结构 `config/{instance_idx}/{group_id}/`
- [ ] 实现`use_group_config`配置加载逻辑
- [ ] 编写配置迁移工具

#### 阶段3: 应用改造 (1-2周)
- [ ] 改造`OneDragonApp`按账号执行默认应用组
- [ ] 实现应用注册机制
- [ ] 确保运行记录系统兼容性

#### 阶段4: 测试和优化 (1周)
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 确保向后兼容性

## 6. 执行引擎设计

### 6.1 增强的执行流程

考虑运行记录后，执行流程需要增加应用可执行性检查：

```mermaid
graph TD
    A["开始执行"] --> B["加载应用组管理器配置"]
    B --> C["获取启用的应用组列表"]
    C --> D["按顺序执行应用组"]
    D --> E["加载应用组配置"]
    E --> F["获取应用组内启用的应用"]
    F --> G["选择下一个应用"]
    G --> H["检查应用运行记录"]
    H --> I{"应用是否需要执行?"}
    I -->|否| J["跳过应用"]
    I -->|是| K["创建应用实例"]
    K --> L["应用配置覆盖"]
    L --> M["执行应用"]
    M --> N{"执行成功?"}
    N -->|是| O["更新运行记录为成功"]
    N -->|否| P["更新运行记录为失败"]
    P --> Q{"是否重试?"}
    Q -->|是| M
    Q -->|否| R{"是否停止应用组?"}
    R -->|是| S["停止应用组执行"]
    R -->|否| T["继续下一个应用"]

    O --> T
    J --> T
    T --> U{"还有更多应用?"}
    U -->|是| G
    U -->|否| V["应用组执行完成"]

    V --> W{"还有更多应用组?"}
    W -->|是| D
    W -->|否| X["全部执行完成"]

    S --> Y["清理资源"]
    Y --> X

    style H fill:#e1f5fe
    style I fill:#fce4ec
    style N fill:#e1f5fe
    style Q fill:#fce4ec
    style R fill:#fce4ec
```

### 6.2 应用执行决策逻辑

```python
class ApplicationExecutionDecision:
    """应用执行决策"""

    def __init__(self, group_app: GroupApplication):
        self.group_app = group_app
        self.should_execute = False
        self.skip_reason = ""
        self.execution_priority = 0

    def evaluate(self) -> bool:
        """评估应用是否应该执行"""
        # 1. 检查应用是否在应用组中启用
        if not self.group_app.enabled_in_group:
            self.skip_reason = "应用在应用组中被禁用"
            return False

        # 2. 检查运行记录
        if not self.group_app.should_run_in_group():
            self.skip_reason = "根据运行记录，应用暂时不需要执行"
            return False

        # 3. 检查应用特定条件(如体力、次数等)
        if hasattr(self.group_app.app, 'can_execute_now'):
            can_execute, reason = self.group_app.app.can_execute_now()
            if not can_execute:
                self.skip_reason = f"应用特定条件不满足: {reason}"
                return False

        # 4. 检查冷却时间
        if self.group_app.group_run_record:
            if not self.group_app.group_run_record.can_run_now():
                self.skip_reason = "应用处于冷却期"
                return False

        self.should_execute = True
        return True

def create_execution_plan(group: ApplicationGroup) -> list[GroupApplication]:
    """创建应用组执行计划"""
    execution_plan = []

    for group_app in group.applications:
        decision = ApplicationExecutionDecision(group_app)
        if decision.evaluate():
            execution_plan.append(group_app)
        else:
            log.info(f"跳过应用 {group_app.app.app_id}: {decision.skip_reason}")

    # 按执行顺序排序
    execution_plan.sort(key=lambda x: x.execution_order)
    return execution_plan
```

### 6.3 状态管理

#### 5.2.1 执行状态枚举

```python
class GroupExecutionStatus(Enum):
    """应用组执行状态"""
    WAITING = "等待执行"
    RUNNING = "正在执行"
    SUCCESS = "执行成功"
    FAILED = "执行失败"
    CANCELLED = "已取消"
    PAUSED = "已暂停"

class ApplicationExecutionStatus(Enum):
    """应用执行状态"""
    WAITING = "等待执行"
    RUNNING = "正在执行"
    SUCCESS = "执行成功"
    FAILED = "执行失败"
    SKIPPED = "已跳过"
```

#### 5.2.2 执行记录

```python
class GroupExecutionRecord:
    """应用组执行记录"""

    def __init__(self, group_id: str):
        self.group_id: str = group_id
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.status: GroupExecutionStatus = GroupExecutionStatus.WAITING
        self.application_records: dict[str, ApplicationExecutionRecord] = {}
        self.error_message: Optional[str] = None

    def start_execution(self) -> None:
        """开始执行"""

    def complete_execution(self, success: bool, error_message: str = None) -> None:
        """完成执行"""

    def get_execution_summary(self) -> dict:
        """获取执行摘要"""
```

## 7. 运行记录配置示例

### 7.1 不同类型应用的配置示例

#### 7.1.1 每次都运行的应用(如体力刷本)

```yaml
# config/01/application_groups/farming/charge_plan_in_farming.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "always"
  record_period: "daily"
  max_daily_runs: -1
  cooldown_minutes: 0
```

#### 7.1.2 每日一次的应用(如邮件)

```yaml
# config/01/application_groups/daily/email_in_daily.yml
enabled: true
run_record_strategy: "shared"  # 使用全局运行记录
```

#### 7.1.3 有次数限制的应用(如恶名狩猎)

```yaml
# config/01/application_groups/weekly/notorious_hunt_in_weekly.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "custom"
  record_period: "weekly"
  max_weekly_runs: 3
  cooldown_minutes: 0
```

#### 7.1.4 有冷却时间的应用

```yaml
# config/01/application_groups/special/coffee_in_special.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "custom"
  record_period: "daily"
  max_daily_runs: 5
  cooldown_minutes: 30  # 30分钟冷却
```

### 7.2 复杂场景配置

#### 7.2.1 同一应用在不同应用组中的不同配置

```yaml
# 日常应用组中的咖啡应用 - 每天一次
# config/01/application_groups/daily/coffee_in_daily.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "once_per_day"
  record_period: "daily"

# 体力消耗应用组中的咖啡应用 - 每次都运行
# config/01/application_groups/farming/coffee_in_farming.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "always"
  record_period: "daily"
  max_daily_runs: 10
  cooldown_minutes: 5
```

## 8. 向后兼容性

### 8.1 兼容性策略

为确保现有代码的正常运行，新架构将提供以下兼容性支持：

1. **默认应用组**: 自动创建一个"默认应用组"，包含所有现有应用
2. **配置迁移**: 自动将现有的`OneDragonAppConfig`迁移到新的配置结构
3. **API兼容**: 保持现有的`OneDragonApp`接口不变，内部使用新的应用组系统

### 8.2 迁移流程

```mermaid
graph TD
    A["检测现有配置"] --> B["创建默认应用组"]
    B --> C["迁移应用配置"]
    C --> D["更新配置文件"]
    D --> E["验证迁移结果"]
    E --> F["完成迁移"]
```

### 8.3 运行记录迁移

对于现有的应用运行记录，需要特别处理：

```python
class RunRecordMigration:
    """运行记录迁移工具"""

    def migrate_to_group_system(self, ctx: OneDragonContext):
        """将现有运行记录迁移到应用组系统"""

        # 1. 创建默认应用组
        default_group = self.create_default_group(ctx)

        # 2. 迁移现有应用到默认应用组
        for app in self.get_existing_apps(ctx):
            group_app = GroupApplication(app, default_group.group_id)

            # 默认使用共享运行记录模式
            group_app.group_config.run_record_strategy = "shared"

            default_group.add_application(group_app)

        # 3. 保存配置
        default_group.config.save()

    def create_default_group(self, ctx: OneDragonContext) -> ApplicationGroup:
        """创建默认应用组"""
        group = ApplicationGroup("default", "默认应用组", ctx)

        # 设置为与原有行为一致的配置
        group.config.retry_failed_applications = True
        group.config.stop_on_first_failure = False

        return group
```

## 9. 配置文件示例

### 9.1 应用组管理器配置示例

```yaml
# config/01/application_group_manager.yml
enabled_groups:
  - "daily_tasks"
  - "weekly_tasks"
  - "special_events"

group_execution_order:
  - "daily_tasks"
  - "weekly_tasks"
  - "special_events"

concurrent_execution: false
```

### 9.2 应用组配置示例

```yaml
# config/01/application_groups/group_daily_tasks.yml
group_name: "日常任务"
enabled_applications:
  - "email"
  - "coffee"
  - "scratch_card"

application_execution_order:
  - "email"
  - "coffee"
  - "scratch_card"

retry_failed_applications: true
stop_on_first_failure: false
```

### 9.3 组内应用配置示例

```yaml
# config/01/application_groups/daily_tasks/coffee_in_daily_tasks.yml
enabled: true
timeout_seconds: 300
retry_times: 2

custom_config_overrides:
  choose_way: "plan_priority"
  challenge_way: "all"
  card_num: "1"
```

## 9. 实现路径

### 9.1 开发阶段

#### 阶段1: 核心架构实现
- [ ] 实现`ApplicationGroup`类
- [ ] 实现`GroupApplication`类
- [ ] 实现`ApplicationGroupManager`类
- [ ] 实现配置系统(`ApplicationGroupConfig`等)

#### 阶段2: 执行引擎实现
- [ ] 实现应用组执行逻辑
- [ ] 实现状态管理系统
- [ ] 实现错误处理和重试机制
- [ ] 实现执行记录和日志

#### 阶段3: 向后兼容性
- [ ] 实现配置迁移工具
- [ ] 实现默认应用组创建
- [ ] 确保现有API兼容性
- [ ] 编写迁移测试用例

#### 阶段4: UI界面实现
- [ ] 设计应用组管理界面
- [ ] 实现应用组创建/编辑功能
- [ ] 实现执行状态监控界面
- [ ] 实现应用配置界面

#### 阶段5: 测试和优化
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 性能优化
- [ ] 用户体验优化

### 9.2 关键技术点

#### 9.2.1 配置覆盖机制
实现应用在不同应用组中的配置覆盖：

```python
def get_effective_config(self, app_id: str, group_id: str) -> dict:
    """获取应用在特定应用组中的有效配置"""
    base_config = self.get_app_base_config(app_id)
    group_config = self.get_group_app_config(group_id, app_id)

    # 合并配置，组配置覆盖基础配置
    effective_config = {**base_config, **group_config.custom_config_overrides}
    return effective_config
```

#### 9.2.2 动态应用实例化
支持应用在不同应用组中的独立实例化：

```python
def create_app_instance(self, app_class: type, group_id: str) -> Application:
    """为特定应用组创建应用实例"""
    app = app_class(self.ctx)

    # 应用组特定的配置
    group_config = self.get_group_app_config(group_id, app.app_id)
    if group_config.custom_config_overrides:
        app.apply_config_overrides(group_config.custom_config_overrides)

    return app
```

#### 9.2.3 状态同步机制
确保UI界面能实时反映执行状态：

```python
class ExecutionStatusManager:
    """执行状态管理器"""

    def __init__(self):
        self.status_listeners: list[Callable] = []
        self.current_status: dict = {}

    def update_status(self, group_id: str, app_id: str, status: str) -> None:
        """更新执行状态"""
        self.current_status[f"{group_id}:{app_id}"] = status
        self.notify_listeners(group_id, app_id, status)

    def notify_listeners(self, group_id: str, app_id: str, status: str) -> None:
        """通知状态监听器"""
        for listener in self.status_listeners:
            listener(group_id, app_id, status)
```

## 10. 总结

### 10.1 架构优势

新的应用组架构具有以下优势：

1. **灵活性**: 用户可以根据需要创建多个应用组，每个应用组包含不同的应用组合
2. **可配置性**: 每个应用在不同应用组中可以有不同的配置，满足多样化需求
3. **智能调度**: 基于运行记录的智能执行决策，避免不必要的重复执行
4. **精确控制**: 支持复杂的执行频率、次数限制、冷却时间等控制策略
5. **可扩展性**: 架构设计支持未来功能的扩展，如并发执行、条件执行等
6. **向后兼容**: 现有代码无需修改即可正常运行
7. **用户友好**: 提供直观的UI界面，降低使用门槛

### 10.2 应用场景

该架构特别适用于以下场景：

1. **多账号管理**: 不同账号需要执行不同的应用组合
2. **时间段任务**: 不同时间段执行不同的应用组
3. **频率控制**: 同一应用在不同场景下有不同的执行频率需求
4. **资源管理**: 根据游戏资源(体力、次数等)智能调度任务执行
5. **条件执行**: 根据游戏状态或外部条件选择执行不同的应用组
6. **测试环境**: 为测试创建专门的应用组，避免影响正常使用

### 10.3 未来扩展

基于这个架构，未来可以考虑以下扩展：

1. **条件执行**: 支持基于条件的应用组执行
2. **并发执行**: 支持多个应用组并发执行
3. **定时执行**: 支持应用组的定时执行
4. **远程管理**: 支持通过网络接口管理应用组
5. **模板系统**: 支持应用组模板的导入导出

## 11. 总结

### 11.1 架构优势

新的应用组架构具有以下优势：

1. **简洁性**: 基于8个核心要点的清晰设计，易于理解和实现
2. **职责分离**: OneDragonAppGroupManager专门管理配置，OneDragonGroupApp专门负责执行
3. **灵活配置**: 通过`use_group_config`精确控制哪些应用使用应用组独立配置
4. **统一运行记录**: 运行记录通过app_id注册，保持现有结构不变
5. **向后兼容**: OneDragonApp改造为执行默认应用组，现有代码无需修改
6. **清晰的文件组织**: `config/{instance_idx}/{group_id}/`结构清晰易懂

### 11.2 核心特性

#### 11.2.1 应用注册机制
- 通过`OneDragonContext`统一管理应用和运行记录的创建函数
- 支持动态应用实例化
- 运行记录通过app_id注册，应用可以获取创建

#### 11.2.2 应用组配置
- 应用组配置只包含`app_ids`和`use_group_config`，保持简洁
- `use_group_config`精确控制配置加载逻辑
- 配置文件按`config/{instance_idx}/{group_id}/`组织

#### 11.2.3 执行机制
- OneDragonGroupApp专门负责应用组执行
- OneDragonApp改造为按账号执行默认应用组
- 保持现有API兼容性

### 11.3 设计价值

1. **开发效率**: 简化的设计降低开发复杂度
2. **维护性**: 职责分离，模块化设计便于维护
3. **用户体验**: 灵活的应用组管理满足不同需求
4. **系统稳定性**: 统一的运行记录避免状态冲突
5. **扩展性**: 为未来功能扩展奠定良好基础

这个设计为游戏自动化项目提供了一个强大而简洁的任务管理框架，在保持系统简洁性的同时，为用户提供了足够的灵活性和控制力。