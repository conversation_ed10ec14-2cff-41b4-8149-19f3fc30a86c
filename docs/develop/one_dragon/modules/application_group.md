# 应用任务组(ApplicationGroup)设计文档

## 1. 项目概述

本文档描述了将当前的OneDragonApp升级为支持多任务组模式的设计方案。新的架构将允许用户创建多个任务组，每个任务组可以包含多个Application，每个Application在不同任务组中可以有独立的配置。

## 2. 设计方案概述

### 2.1 核心设计理念

基于以下8个核心要点的改造方案：

1. **应用注册机制**: `OneDragonContext` 新增应用注册方法，可以注册应用的创建函数
2. **任务组执行器**: 新增 `OneDragonGroupApp`，类似于当前的 `OneDragonApp`，用于执行一个任务组
3. **任务组配置**: `OneDragonGroupApp` 接受一个 `OneDragonGroupConfig`，包含具体的应用ID列表
4. **动态应用创建**: `OneDragonGroupApp` 按任务ID从 `OneDragonContext` 创建应用并执行
5. **向后兼容**: 当前 `OneDragonApp` 改造为 `group_id=one_dragon` 的默认任务组
6. **分层配置**: 应用配置按 `instance_idx` 和 `group_id` 独立，支持配置继承
7. **统一运行记录**: 运行记录保持当前结构，按账号独立，不区分任务组
8. **任务组管理**: `OneDragonAppGroupManager` 管理任务组的增删改查

### 2.2 架构对比

#### 2.2.1 当前架构
```mermaid
graph TD
    A["OneDragonApp"] --> B["固定应用列表"]
    B --> C["Application实例"]
    C --> D["应用配置"]
    C --> E["运行记录"]
```

#### 2.2.2 新架构
```mermaid
graph TD
    A["OneDragonAppGroupManager"] --> B["OneDragonGroupApp"]
    B --> C["OneDragonGroupConfig"]
    C --> D["应用ID列表"]
    B --> E["OneDragonContext"]
    E --> F["应用注册表"]
    F --> G["动态创建Application"]
    G --> H["分层配置系统"]
    G --> I["统一运行记录"]
```

## 3. 核心组件设计

### 3.1 整体架构

```mermaid
graph TD
    A["OneDragonAppGroupManager<br/>任务组管理器"] --> B["OneDragonGroupApp<br/>任务组执行器"]
    B --> C["OneDragonGroupConfig<br/>任务组配置"]
    C --> D["应用ID列表"]

    B --> E["OneDragonContext<br/>上下文"]
    E --> F["应用注册表<br/>AppRegistry"]
    F --> G["应用创建函数"]

    G --> H["Application实例"]
    H --> I["分层配置系统"]
    H --> J["统一运行记录"]

    K["OneDragonInstance<br/>实例管理"] --> I
    K --> J
```

### 3.2 核心类设计

#### 3.2.1 OneDragonContext 增强

```python
class OneDragonContext:
    """增强的上下文类，支持应用注册"""

    def __init__(self, controller: Optional[ControllerBase] = None):
        # ... 现有初始化代码 ...
        self.app_registry: AppRegistry = AppRegistry()

    def register_app(self, app_id: str, app_factory: Callable[[], Application]) -> None:
        """注册应用创建函数

        Args:
            app_id: 应用唯一标识
            app_factory: 应用创建函数，返回Application实例
        """
        self.app_registry.register(app_id, app_factory)

    def create_app(self, app_id: str, group_id: str = "one_dragon") -> Optional[Application]:
        """根据应用ID创建应用实例

        Args:
            app_id: 应用ID
            group_id: 任务组ID，用于配置隔离

        Returns:
            Application实例，如果应用ID不存在则返回None
        """
        return self.app_registry.create_app(app_id, self, group_id)

    def get_registered_app_ids(self) -> list[str]:
        """获取所有已注册的应用ID"""
        return self.app_registry.get_app_ids()
```

#### 3.2.2 AppRegistry (应用注册表)

```python
class AppRegistry:
    """应用注册表，管理应用的创建函数"""

    def __init__(self):
        self._app_factories: dict[str, Callable[[OneDragonContext, str], Application]] = {}

    def register(self, app_id: str, app_factory: Callable[[OneDragonContext, str], Application]) -> None:
        """注册应用创建函数

        Args:
            app_id: 应用唯一标识
            app_factory: 应用创建函数，接受(ctx, group_id)参数，返回Application实例
        """
        if app_id in self._app_factories:
            raise ValueError(f"应用ID '{app_id}' 已经注册")
        self._app_factories[app_id] = app_factory

    def create_app(self, app_id: str, ctx: OneDragonContext, group_id: str) -> Optional[Application]:
        """创建应用实例

        Args:
            app_id: 应用ID
            ctx: 上下文
            group_id: 任务组ID

        Returns:
            Application实例，如果应用ID不存在则返回None
        """
        if app_id not in self._app_factories:
            return None

        return self._app_factories[app_id](ctx, group_id)

    def get_app_ids(self) -> list[str]:
        """获取所有已注册的应用ID"""
        return list(self._app_factories.keys())

    def is_registered(self, app_id: str) -> bool:
        """检查应用是否已注册"""
        return app_id in self._app_factories
```

#### 3.2.3 OneDragonGroupApp (任务组执行器)

```python
class OneDragonGroupApp(Application):
    """任务组执行器，类似于当前的OneDragonApp"""

    def __init__(self, ctx: OneDragonContext, group_config: OneDragonGroupConfig):
        Application.__init__(self, ctx, f"group_{group_config.group_id}")
        self.group_config: OneDragonGroupConfig = group_config
        self.ctx: OneDragonContext = ctx

    def execute(self) -> OperationResult:
        """执行任务组中的所有应用"""
        result = OperationResult(True)

        for app_id in self.group_config.app_ids:
            # 从上下文创建应用实例
            app = self.ctx.create_app(app_id, self.group_config.group_id)
            if app is None:
                log.warning(f"应用 {app_id} 未注册，跳过执行")
                continue

            # 检查应用是否需要执行
            if not self._should_run_app(app):
                log.info(f"应用 {app_id} 暂时不需要执行，跳过")
                continue

            # 执行应用
            app_result = app.execute()
            if not app_result.success:
                log.error(f"应用 {app_id} 执行失败: {app_result.data}")
                if self.group_config.stop_on_first_failure:
                    result.success = False
                    result.data = f"任务组执行失败，应用 {app_id} 执行失败"
                    break

        return result

    def _should_run_app(self, app: Application) -> bool:
        """判断应用是否应该执行"""
        if hasattr(app, 'run_record') and app.run_record is not None:
            app.run_record.check_and_update_status()
            return app.run_record.run_status_under_now != AppRunRecord.STATUS_SUCCESS
        return True

    def get_app_list(self) -> list[str]:
        """获取任务组中的应用ID列表"""
        return self.group_config.app_ids.copy()
```

#### 3.2.4 OneDragonAppGroupManager (任务组管理器)

```python
class OneDragonAppGroupManager:
    """任务组管理器，负责管理所有任务组的增删改查"""

    BUILTIN_GROUP_ID = "one_dragon"  # 内置任务组ID

    def __init__(self, ctx: OneDragonContext):
        self.ctx: OneDragonContext = ctx
        self._groups: dict[str, OneDragonGroupConfig] = {}
        self._load_all_groups()

    def create_group(self, group_id: str, group_name: str, app_ids: list[str] = None) -> OneDragonGroupConfig:
        """创建新的任务组

        Args:
            group_id: 任务组ID
            group_name: 任务组名称
            app_ids: 应用ID列表

        Returns:
            创建的任务组配置

        Raises:
            ValueError: 如果任务组ID已存在
        """
        if group_id in self._groups:
            raise ValueError(f"任务组ID '{group_id}' 已存在")

        config = OneDragonGroupConfig(group_id, self.ctx.current_instance_idx)
        config.group_name = group_name
        if app_ids:
            config.app_ids = app_ids
        config.save()

        self._groups[group_id] = config
        return config

    def delete_group(self, group_id: str) -> bool:
        """删除任务组

        Args:
            group_id: 任务组ID

        Returns:
            是否删除成功

        Raises:
            ValueError: 如果尝试删除内置任务组
        """
        if group_id == self.BUILTIN_GROUP_ID:
            raise ValueError(f"不能删除内置任务组 '{self.BUILTIN_GROUP_ID}'")

        if group_id not in self._groups:
            return False

        # 删除配置文件
        config = self._groups[group_id]
        config.delete()

        # 从内存中移除
        del self._groups[group_id]
        return True

    def get_group(self, group_id: str) -> Optional[OneDragonGroupConfig]:
        """获取任务组配置"""
        return self._groups.get(group_id)

    def get_all_groups(self) -> dict[str, OneDragonGroupConfig]:
        """获取所有任务组配置"""
        return self._groups.copy()

    def update_group(self, group_id: str, **kwargs) -> bool:
        """更新任务组配置

        Args:
            group_id: 任务组ID
            **kwargs: 要更新的配置项

        Returns:
            是否更新成功
        """
        if group_id not in self._groups:
            return False

        config = self._groups[group_id]
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)

        config.save()
        return True

    def create_group_app(self, group_id: str) -> Optional[OneDragonGroupApp]:
        """创建任务组执行器

        Args:
            group_id: 任务组ID

        Returns:
            任务组执行器实例，如果任务组不存在则返回None
        """
        config = self.get_group(group_id)
        if config is None:
            return None

        return OneDragonGroupApp(self.ctx, config)

    def _load_all_groups(self):
        """加载所有任务组配置"""
        # 确保内置任务组存在
        builtin_config = OneDragonGroupConfig(self.BUILTIN_GROUP_ID, self.ctx.current_instance_idx)
        if not builtin_config.exists():
            # 创建默认的内置任务组配置
            builtin_config.group_name = "默认任务组"
            builtin_config.app_ids = self._get_default_app_ids()
            builtin_config.save()

        self._groups[self.BUILTIN_GROUP_ID] = builtin_config

        # 加载其他任务组配置
        # TODO: 实现从配置目录扫描所有任务组配置文件

    def _get_default_app_ids(self) -> list[str]:
        """获取默认的应用ID列表（从当前OneDragonApp迁移）"""
        # TODO: 从当前OneDragonApp获取应用列表
        return []
```

## 4. 配置系统设计

### 4.1 配置层次结构

```mermaid
graph TD
    A["OneDragonGroupConfig<br/>任务组配置"] --> B["应用ID列表"]

    C["应用配置<br/>(CoffeeConfig等)"] --> D["默认配置<br/>group_id=one_dragon"]
    C --> E["任务组特定配置<br/>group_id=custom"]

    F["OneDragonInstance<br/>实例"] --> A
    F --> C

    G["配置继承关系"] --> H["group_id=one_dragon<br/>作为默认配置"]
    H --> I["其他group_id<br/>继承默认配置"]
```

### 4.2 核心配置类

#### 4.2.1 OneDragonGroupConfig (任务组配置)

```python
class OneDragonGroupConfig(YamlConfig):
    """任务组配置"""

    def __init__(self, group_id: str, instance_idx: Optional[int] = None):
        YamlConfig.__init__(self, f'group_{group_id}', instance_idx=instance_idx, sub_dir=['groups'])
        self.group_id = group_id

    @property
    def group_name(self) -> str:
        """任务组名称"""
        return self.get("group_name", f"任务组_{self.group_id}")

    @group_name.setter
    def group_name(self, value: str):
        self.update("group_name", value)

    @property
    def app_ids(self) -> list[str]:
        """应用ID列表"""
        return self.get("app_ids", [])

    @app_ids.setter
    def app_ids(self, value: list[str]):
        self.update("app_ids", value)

    @property
    def stop_on_first_failure(self) -> bool:
        """首次失败时是否停止执行"""
        return self.get("stop_on_first_failure", False)

    @stop_on_first_failure.setter
    def stop_on_first_failure(self, value: bool):
        self.update("stop_on_first_failure", value)

    @property
    def enabled(self) -> bool:
        """任务组是否启用"""
        return self.get("enabled", True)

    @enabled.setter
    def enabled(self, value: bool):
        self.update("enabled", value)

    def add_app(self, app_id: str) -> None:
        """添加应用到任务组"""
        current_apps = self.app_ids
        if app_id not in current_apps:
            current_apps.append(app_id)
            self.app_ids = current_apps

    def remove_app(self, app_id: str) -> None:
        """从任务组移除应用"""
        current_apps = self.app_ids
        if app_id in current_apps:
            current_apps.remove(app_id)
            self.app_ids = current_apps

    def move_app(self, app_id: str, new_index: int) -> bool:
        """移动应用在任务组中的位置"""
        current_apps = self.app_ids
        if app_id not in current_apps:
            return False

        current_apps.remove(app_id)
        current_apps.insert(new_index, app_id)
        self.app_ids = current_apps
        return True
```

### 4.2 应用配置的分层机制

基于方案要求，每个应用的配置将按 `instance_idx` 和 `group_id` 独立，但 `group_id=one_dragon` 为默认配置。

#### 4.2.1 GroupAwareConfig (支持任务组的配置基类)

```python
class GroupAwareConfig(YamlConfig):
    """支持任务组的配置基类"""

    def __init__(self, config_name: str, group_id: str = "one_dragon", instance_idx: Optional[int] = None):
        # 根据group_id构建配置文件路径
        if group_id == "one_dragon":
            # 默认配置直接放在根目录
            super().__init__(config_name, instance_idx=instance_idx)
        else:
            # 任务组特定配置放在groups子目录
            super().__init__(config_name, instance_idx=instance_idx, sub_dir=['groups', group_id])

        self.group_id = group_id
        self.default_config: Optional[GroupAwareConfig] = None

        # 如果不是默认配置，加载默认配置作为回退
        if group_id != "one_dragon":
            self.default_config = self.__class__(config_name, "one_dragon", instance_idx)

    def get(self, key: str, default_value=None):
        """获取配置值，支持从默认配置继承"""
        # 首先尝试从当前配置获取
        value = super().get(key, None)
        if value is not None:
            return value

        # 如果当前配置没有，且有默认配置，从默认配置获取
        if self.default_config is not None:
            return self.default_config.get(key, default_value)

        return default_value

    def has_custom_value(self, key: str) -> bool:
        """检查是否有自定义值（不是从默认配置继承的）"""
        return super().get(key, None) is not None

#### 4.2.2 应用配置示例

```python
# 应用配置示例 - 咖啡应用
class CoffeeConfig(GroupAwareConfig):
    """咖啡应用配置，支持任务组"""

    def __init__(self, group_id: str = "one_dragon", instance_idx: Optional[int] = None):
        GroupAwareConfig.__init__(self, 'coffee', group_id, instance_idx)

    @property
    def choose_way(self) -> str:
        """选择方式"""
        return self.get("choose_way", "plan_priority")

    @choose_way.setter
    def choose_way(self, value: str):
        self.update("choose_way", value)

    @property
    def challenge_way(self) -> str:
        """挑战方式"""
        return self.get("challenge_way", "all")

    @challenge_way.setter
    def challenge_way(self, value: str):
        self.update("challenge_way", value)

# 应用配置示例 - 邮件应用
class EmailConfig(GroupAwareConfig):
    """邮件应用配置，支持任务组"""

    def __init__(self, group_id: str = "one_dragon", instance_idx: Optional[int] = None):
        GroupAwareConfig.__init__(self, 'email', group_id, instance_idx)

    @property
    def auto_delete(self) -> bool:
        """是否自动删除邮件"""
        return self.get("auto_delete", True)

    @auto_delete.setter
    def auto_delete(self, value: bool):
        self.update("auto_delete", value)
```

#### 4.2.3 Application基类的增强

```python
class Application:
    """应用基类增强，支持任务组配置"""

    def __init__(self, ctx: OneDragonContext, app_id: str, group_id: str = "one_dragon"):
        self.ctx = ctx
        self.app_id = app_id
        self.group_id = group_id

        # 初始化配置（子类需要重写此方法）
        self.config = self._create_config(group_id)

        # 运行记录保持原有逻辑，不区分任务组
        self.run_record = self._create_run_record()

    def _create_config(self, group_id: str):
        """创建配置实例，子类需要重写"""
        raise NotImplementedError("子类必须实现_create_config方法")

    def _create_run_record(self):
        """创建运行记录实例，子类可以重写"""
        return None

    def setup_group_config(self, ctx: OneDragonContext, group_id: str):
        """设置任务组特定配置"""
        self.ctx = ctx
        self.group_id = group_id
        self.config = self._create_config(group_id)

# 具体应用示例
class CoffeeApp(Application):
    """咖啡应用"""

    def __init__(self, ctx: OneDragonContext, group_id: str = "one_dragon"):
        Application.__init__(self, ctx, "coffee", group_id)

    def _create_config(self, group_id: str) -> CoffeeConfig:
        """创建咖啡应用配置"""
        return CoffeeConfig(group_id, self.ctx.current_instance_idx)

    def _create_run_record(self):
        """创建运行记录"""
        return AppRunRecord("coffee", instance_idx=self.ctx.current_instance_idx)

    def execute(self) -> OperationResult:
        """执行咖啡应用逻辑"""
        # 使用self.config获取配置
        choose_way = self.config.choose_way
        challenge_way = self.config.challenge_way

        # 执行具体逻辑...
        return OperationResult(True)
```
```

## 5. 应用注册机制

### 5.1 应用注册示例

```python
def register_all_apps(ctx: OneDragonContext):
    """注册所有应用到上下文"""

    # 注册咖啡应用
    ctx.register_app("coffee", lambda ctx, group_id: CoffeeApp(ctx, group_id))

    # 注册邮件应用
    ctx.register_app("email", lambda ctx, group_id: EmailApp(ctx, group_id))

    # 注册体力应用
    ctx.register_app("charge_plan", lambda ctx, group_id: ChargePlanApp(ctx, group_id))

    # 注册其他应用...
    ctx.register_app("scratch_card", lambda ctx, group_id: ScratchCardApp(ctx, group_id))
    ctx.register_app("notorious_hunt", lambda ctx, group_id: NotoriousHuntApp(ctx, group_id))
```

### 5.2 应用工厂函数

```python
class AppFactory:
    """应用工厂类，统一管理应用创建"""

    @staticmethod
    def create_coffee_app(ctx: OneDragonContext, group_id: str) -> CoffeeApp:
        """创建咖啡应用"""
        return CoffeeApp(ctx, group_id)

    @staticmethod
    def create_email_app(ctx: OneDragonContext, group_id: str) -> EmailApp:
        """创建邮件应用"""
        return EmailApp(ctx, group_id)

    @staticmethod
    def register_all(ctx: OneDragonContext):
        """注册所有应用"""
        ctx.register_app("coffee", AppFactory.create_coffee_app)
        ctx.register_app("email", AppFactory.create_email_app)
        # ... 注册其他应用
```

## 6. 运行记录系统

### 6.1 运行记录保持现有结构

根据方案要求，运行记录保持当前结构不变，即运行记录是账号下独立，不区分任务组。这意味着：

1. **统一性**: 同一个应用在所有任务组中共享同一个运行记录
2. **避免重复**: 如果应用在一个任务组中已经执行成功，在其他任务组中会被跳过
3. **简化管理**: 不需要为每个任务组维护独立的运行记录

### 6.2 运行记录示例

```python
class CoffeeApp(Application):
    """咖啡应用示例"""

    def _create_run_record(self):
        """创建运行记录 - 不区分任务组"""
        return AppRunRecord("coffee", instance_idx=self.ctx.current_instance_idx)

    def execute(self) -> OperationResult:
        """执行应用"""
        # 检查运行记录
        if self.run_record:
            self.run_record.check_and_update_status()
            if self.run_record.run_status_under_now == AppRunRecord.STATUS_SUCCESS:
                return OperationResult(True, "应用已完成，跳过执行")

        # 执行具体逻辑
        result = self._do_execute()

        # 更新运行记录
        if result.success and self.run_record:
            self.run_record.update_status(AppRunRecord.STATUS_SUCCESS)

        return result

    def _do_execute(self) -> OperationResult:
        """具体的执行逻辑"""
        # 使用任务组特定的配置
        choose_way = self.config.choose_way
        challenge_way = self.config.challenge_way

        # 执行咖啡相关操作...
        return OperationResult(True)
```
```

## 7. 使用示例

### 7.1 完整的使用流程

```python
def main():
    """主程序示例"""
    # 1. 创建上下文
    ctx = OneDragonContext()

    # 2. 注册所有应用
    register_all_apps(ctx)

    # 3. 创建任务组管理器
    group_manager = OneDragonAppGroupManager(ctx)

    # 4. 创建自定义任务组
    daily_group = group_manager.create_group(
        group_id="daily_tasks",
        group_name="日常任务",
        app_ids=["email", "coffee", "scratch_card"]
    )

    farming_group = group_manager.create_group(
        group_id="farming",
        group_name="体力消耗",
        app_ids=["charge_plan", "coffee"]
    )

    # 5. 执行任务组
    daily_app = group_manager.create_group_app("daily_tasks")
    if daily_app:
        result = daily_app.execute()
        print(f"日常任务执行结果: {result.success}")

    farming_app = group_manager.create_group_app("farming")
    if farming_app:
        result = farming_app.execute()
        print(f"体力消耗执行结果: {result.success}")
```

### 7.2 配置文件示例

#### 7.2.1 任务组配置

```yaml
# config/01/groups/group_daily_tasks.yml
group_name: "日常任务"
app_ids:
  - "email"
  - "coffee"
  - "scratch_card"
enabled: true
stop_on_first_failure: false
```

```yaml
# config/01/groups/group_farming.yml
group_name: "体力消耗"
app_ids:
  - "charge_plan"
  - "coffee"
enabled: true
stop_on_first_failure: false
```

#### 7.2.2 应用配置示例

```yaml
# config/01/coffee.yml (默认配置)
choose_way: "plan_priority"
challenge_way: "all"
card_num: "1"
```

```yaml
# config/01/groups/farming/coffee.yml (farming任务组中的特定配置)
choose_way: "always_challenge"  # 覆盖默认配置
challenge_way: "all"            # 继承默认配置
# card_num 未设置，继承默认配置的 "1"
```

## 8. 向后兼容性

### 8.1 OneDragonApp的改造

当前的`OneDragonApp`将被改造为使用`OneDragonGroupApp`的默认任务组：

```python
class OneDragonApp:
    """改造后的OneDragonApp，使用默认任务组"""

    def __init__(self, ctx: OneDragonContext):
        self.ctx = ctx
        self.group_manager = OneDragonAppGroupManager(ctx)

        # 获取默认任务组的执行器
        self.group_app = self.group_manager.create_group_app("one_dragon")
        if self.group_app is None:
            raise RuntimeError("无法创建默认任务组")

    def execute(self) -> OperationResult:
        """执行默认任务组"""
        return self.group_app.execute()

    def get_app_list(self) -> list[str]:
        """获取应用列表（保持兼容性）"""
        return self.group_app.get_app_list()

    def get_one_dragon_apps_in_order(self) -> list[str]:
        """获取按顺序排列的应用列表（保持兼容性）"""
        return self.get_app_list()
```

### 8.2 配置迁移

```python
class ConfigMigration:
    """配置迁移工具"""

    def __init__(self, ctx: OneDragonContext):
        self.ctx = ctx

    def migrate_from_old_config(self):
        """从旧配置迁移到新配置"""
        # 1. 读取旧的OneDragonAppConfig
        old_config = OneDragonAppConfig(self.ctx.current_instance_idx)

        # 2. 创建默认任务组配置
        default_group = OneDragonGroupConfig("one_dragon", self.ctx.current_instance_idx)
        default_group.group_name = "默认任务组"

        # 3. 迁移应用列表
        if hasattr(old_config, 'get_app_run_order'):
            app_ids = old_config.get_app_run_order()
            default_group.app_ids = app_ids

        # 4. 迁移其他配置
        default_group.stop_on_first_failure = False  # 保持原有行为
        default_group.enabled = True

        # 5. 保存配置
        default_group.save()

        print("配置迁移完成")

    def check_migration_needed(self) -> bool:
        """检查是否需要迁移"""
        default_group = OneDragonGroupConfig("one_dragon", self.ctx.current_instance_idx)
        return not default_group.exists()
```

## 9. 实现路径

### 9.1 开发阶段

#### 阶段1: 核心架构实现 (1-2周)
- [ ] 实现`AppRegistry`应用注册表
- [ ] 增强`OneDragonContext`，添加应用注册功能
- [ ] 实现`OneDragonGroupConfig`任务组配置
- [ ] 实现`OneDragonGroupApp`任务组执行器
- [ ] 实现`OneDragonAppGroupManager`任务组管理器

#### 阶段2: 配置系统实现 (1周)
- [ ] 实现`GroupAwareConfig`配置基类
- [ ] 改造现有应用配置类，支持任务组
- [ ] 实现配置继承机制
- [ ] 编写配置迁移工具

#### 阶段3: 应用改造 (2-3周)
- [ ] 改造`Application`基类，支持任务组配置
- [ ] 逐个改造现有应用类
- [ ] 实现应用注册机制
- [ ] 确保运行记录系统兼容性

#### 阶段4: 向后兼容性 (1周)
- [ ] 改造`OneDragonApp`使用默认任务组
- [ ] 实现配置自动迁移
- [ ] 确保现有API兼容性
- [ ] 编写兼容性测试用例

#### 阶段5: UI界面实现 (2-3周)
- [ ] 设计任务组管理界面
- [ ] 实现任务组创建/编辑功能
- [ ] 实现应用配置界面
- [ ] 实现执行状态监控

#### 阶段6: 测试和优化 (1-2周)
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 性能优化
- [ ] 用户体验优化

### 9.2 关键实现要点

#### 9.2.1 应用注册时机
```python
# 在应用启动时注册所有应用
def initialize_application_system(ctx: OneDragonContext):
    """初始化应用系统"""
    # 注册所有应用
    register_all_apps(ctx)

    # 检查是否需要配置迁移
    migration = ConfigMigration(ctx)
    if migration.check_migration_needed():
        migration.migrate_from_old_config()

    # 初始化任务组管理器
    group_manager = OneDragonAppGroupManager(ctx)

    return group_manager
```

#### 9.2.2 配置文件组织
```
config/
├── 01/                          # 实例01
│   ├── coffee.yml              # 默认配置 (group_id=one_dragon)
│   ├── email.yml               # 默认配置
│   ├── groups/                 # 任务组配置目录
│   │   ├── group_one_dragon.yml    # 默认任务组配置
│   │   ├── group_daily_tasks.yml   # 日常任务组配置
│   │   ├── group_farming.yml       # 体力消耗任务组配置
│   │   ├── daily_tasks/            # 日常任务组的应用配置
│   │   │   ├── coffee.yml          # 咖啡在日常任务组中的配置
│   │   │   └── email.yml           # 邮件在日常任务组中的配置
│   │   └── farming/                # 体力消耗任务组的应用配置
│   │       ├── coffee.yml          # 咖啡在体力消耗组中的配置
│   │       └── charge_plan.yml     # 体力在体力消耗组中的配置
│   └── ...
└── 02/                          # 实例02
    └── ...
```

#### 9.2.3 错误处理策略
```python
class OneDragonGroupApp(Application):
    def execute(self) -> OperationResult:
        """执行任务组，包含完整的错误处理"""
        result = OperationResult(True)
        failed_apps = []

        for app_id in self.group_config.app_ids:
            try:
                app = self.ctx.create_app(app_id, self.group_config.group_id)
                if app is None:
                    log.warning(f"应用 {app_id} 未注册，跳过执行")
                    continue

                if not self._should_run_app(app):
                    log.info(f"应用 {app_id} 暂时不需要执行，跳过")
                    continue

                app_result = app.execute()
                if not app_result.success:
                    failed_apps.append(app_id)
                    log.error(f"应用 {app_id} 执行失败: {app_result.data}")

                    if self.group_config.stop_on_first_failure:
                        result.success = False
                        result.data = f"任务组执行失败，应用 {app_id} 执行失败"
                        break

            except Exception as e:
                failed_apps.append(app_id)
                log.error(f"应用 {app_id} 执行异常: {str(e)}")

                if self.group_config.stop_on_first_failure:
                    result.success = False
                    result.data = f"任务组执行异常，应用 {app_id}: {str(e)}"
                    break

        if failed_apps and result.success:
            result.data = f"部分应用执行失败: {', '.join(failed_apps)}"

        return result
```

## 6. 执行引擎设计

### 6.1 增强的执行流程

考虑运行记录后，执行流程需要增加应用可执行性检查：

```mermaid
graph TD
    A["开始执行"] --> B["加载任务组管理器配置"]
    B --> C["获取启用的任务组列表"]
    C --> D["按顺序执行任务组"]
    D --> E["加载任务组配置"]
    E --> F["获取任务组内启用的应用"]
    F --> G["选择下一个应用"]
    G --> H["检查应用运行记录"]
    H --> I{"应用是否需要执行?"}
    I -->|否| J["跳过应用"]
    I -->|是| K["创建应用实例"]
    K --> L["应用配置覆盖"]
    L --> M["执行应用"]
    M --> N{"执行成功?"}
    N -->|是| O["更新运行记录为成功"]
    N -->|否| P["更新运行记录为失败"]
    P --> Q{"是否重试?"}
    Q -->|是| M
    Q -->|否| R{"是否停止任务组?"}
    R -->|是| S["停止任务组执行"]
    R -->|否| T["继续下一个应用"]

    O --> T
    J --> T
    T --> U{"还有更多应用?"}
    U -->|是| G
    U -->|否| V["任务组执行完成"]

    V --> W{"还有更多任务组?"}
    W -->|是| D
    W -->|否| X["全部执行完成"]

    S --> Y["清理资源"]
    Y --> X

    style H fill:#e1f5fe
    style I fill:#fce4ec
    style N fill:#e1f5fe
    style Q fill:#fce4ec
    style R fill:#fce4ec
```

### 6.2 应用执行决策逻辑

```python
class ApplicationExecutionDecision:
    """应用执行决策"""

    def __init__(self, group_app: GroupApplication):
        self.group_app = group_app
        self.should_execute = False
        self.skip_reason = ""
        self.execution_priority = 0

    def evaluate(self) -> bool:
        """评估应用是否应该执行"""
        # 1. 检查应用是否在任务组中启用
        if not self.group_app.enabled_in_group:
            self.skip_reason = "应用在任务组中被禁用"
            return False

        # 2. 检查运行记录
        if not self.group_app.should_run_in_group():
            self.skip_reason = "根据运行记录，应用暂时不需要执行"
            return False

        # 3. 检查应用特定条件(如体力、次数等)
        if hasattr(self.group_app.app, 'can_execute_now'):
            can_execute, reason = self.group_app.app.can_execute_now()
            if not can_execute:
                self.skip_reason = f"应用特定条件不满足: {reason}"
                return False

        # 4. 检查冷却时间
        if self.group_app.group_run_record:
            if not self.group_app.group_run_record.can_run_now():
                self.skip_reason = "应用处于冷却期"
                return False

        self.should_execute = True
        return True

def create_execution_plan(group: ApplicationGroup) -> list[GroupApplication]:
    """创建任务组执行计划"""
    execution_plan = []

    for group_app in group.applications:
        decision = ApplicationExecutionDecision(group_app)
        if decision.evaluate():
            execution_plan.append(group_app)
        else:
            log.info(f"跳过应用 {group_app.app.app_id}: {decision.skip_reason}")

    # 按执行顺序排序
    execution_plan.sort(key=lambda x: x.execution_order)
    return execution_plan
```

### 6.3 状态管理

#### 5.2.1 执行状态枚举

```python
class GroupExecutionStatus(Enum):
    """任务组执行状态"""
    WAITING = "等待执行"
    RUNNING = "正在执行"
    SUCCESS = "执行成功"
    FAILED = "执行失败"
    CANCELLED = "已取消"
    PAUSED = "已暂停"

class ApplicationExecutionStatus(Enum):
    """应用执行状态"""
    WAITING = "等待执行"
    RUNNING = "正在执行"
    SUCCESS = "执行成功"
    FAILED = "执行失败"
    SKIPPED = "已跳过"
```

#### 5.2.2 执行记录

```python
class GroupExecutionRecord:
    """任务组执行记录"""

    def __init__(self, group_id: str):
        self.group_id: str = group_id
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.status: GroupExecutionStatus = GroupExecutionStatus.WAITING
        self.application_records: dict[str, ApplicationExecutionRecord] = {}
        self.error_message: Optional[str] = None

    def start_execution(self) -> None:
        """开始执行"""

    def complete_execution(self, success: bool, error_message: str = None) -> None:
        """完成执行"""

    def get_execution_summary(self) -> dict:
        """获取执行摘要"""
```

## 7. 运行记录配置示例

### 7.1 不同类型应用的配置示例

#### 7.1.1 每次都运行的应用(如体力刷本)

```yaml
# config/01/application_groups/farming/charge_plan_in_farming.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "always"
  record_period: "daily"
  max_daily_runs: -1
  cooldown_minutes: 0
```

#### 7.1.2 每日一次的应用(如邮件)

```yaml
# config/01/application_groups/daily/email_in_daily.yml
enabled: true
run_record_strategy: "shared"  # 使用全局运行记录
```

#### 7.1.3 有次数限制的应用(如恶名狩猎)

```yaml
# config/01/application_groups/weekly/notorious_hunt_in_weekly.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "custom"
  record_period: "weekly"
  max_weekly_runs: 3
  cooldown_minutes: 0
```

#### 7.1.4 有冷却时间的应用

```yaml
# config/01/application_groups/special/coffee_in_special.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "custom"
  record_period: "daily"
  max_daily_runs: 5
  cooldown_minutes: 30  # 30分钟冷却
```

### 7.2 复杂场景配置

#### 7.2.1 同一应用在不同任务组中的不同配置

```yaml
# 日常任务组中的咖啡应用 - 每天一次
# config/01/application_groups/daily/coffee_in_daily.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "once_per_day"
  record_period: "daily"

# 体力消耗任务组中的咖啡应用 - 每次都运行
# config/01/application_groups/farming/coffee_in_farming.yml
enabled: true
run_record_strategy: "independent"
independent_record_config:
  run_frequency: "always"
  record_period: "daily"
  max_daily_runs: 10
  cooldown_minutes: 5
```

## 8. 向后兼容性

### 8.1 兼容性策略

为确保现有代码的正常运行，新架构将提供以下兼容性支持：

1. **默认任务组**: 自动创建一个"默认任务组"，包含所有现有应用
2. **配置迁移**: 自动将现有的`OneDragonAppConfig`迁移到新的配置结构
3. **API兼容**: 保持现有的`OneDragonApp`接口不变，内部使用新的任务组系统

### 8.2 迁移流程

```mermaid
graph TD
    A["检测现有配置"] --> B["创建默认任务组"]
    B --> C["迁移应用配置"]
    C --> D["更新配置文件"]
    D --> E["验证迁移结果"]
    E --> F["完成迁移"]
```

### 8.3 运行记录迁移

对于现有的应用运行记录，需要特别处理：

```python
class RunRecordMigration:
    """运行记录迁移工具"""

    def migrate_to_group_system(self, ctx: OneDragonContext):
        """将现有运行记录迁移到任务组系统"""

        # 1. 创建默认任务组
        default_group = self.create_default_group(ctx)

        # 2. 迁移现有应用到默认任务组
        for app in self.get_existing_apps(ctx):
            group_app = GroupApplication(app, default_group.group_id)

            # 默认使用共享运行记录模式
            group_app.group_config.run_record_strategy = "shared"

            default_group.add_application(group_app)

        # 3. 保存配置
        default_group.config.save()

    def create_default_group(self, ctx: OneDragonContext) -> ApplicationGroup:
        """创建默认任务组"""
        group = ApplicationGroup("default", "默认任务组", ctx)

        # 设置为与原有行为一致的配置
        group.config.retry_failed_applications = True
        group.config.stop_on_first_failure = False

        return group
```

## 9. 配置文件示例

### 9.1 任务组管理器配置示例

```yaml
# config/01/application_group_manager.yml
enabled_groups:
  - "daily_tasks"
  - "weekly_tasks"
  - "special_events"

group_execution_order:
  - "daily_tasks"
  - "weekly_tasks"
  - "special_events"

concurrent_execution: false
```

### 9.2 任务组配置示例

```yaml
# config/01/application_groups/group_daily_tasks.yml
group_name: "日常任务"
enabled_applications:
  - "email"
  - "coffee"
  - "scratch_card"

application_execution_order:
  - "email"
  - "coffee"
  - "scratch_card"

retry_failed_applications: true
stop_on_first_failure: false
```

### 9.3 组内应用配置示例

```yaml
# config/01/application_groups/daily_tasks/coffee_in_daily_tasks.yml
enabled: true
timeout_seconds: 300
retry_times: 2

custom_config_overrides:
  choose_way: "plan_priority"
  challenge_way: "all"
  card_num: "1"
```

## 9. 实现路径

### 9.1 开发阶段

#### 阶段1: 核心架构实现
- [ ] 实现`ApplicationGroup`类
- [ ] 实现`GroupApplication`类
- [ ] 实现`ApplicationGroupManager`类
- [ ] 实现配置系统(`ApplicationGroupConfig`等)

#### 阶段2: 执行引擎实现
- [ ] 实现任务组执行逻辑
- [ ] 实现状态管理系统
- [ ] 实现错误处理和重试机制
- [ ] 实现执行记录和日志

#### 阶段3: 向后兼容性
- [ ] 实现配置迁移工具
- [ ] 实现默认任务组创建
- [ ] 确保现有API兼容性
- [ ] 编写迁移测试用例

#### 阶段4: UI界面实现
- [ ] 设计任务组管理界面
- [ ] 实现任务组创建/编辑功能
- [ ] 实现执行状态监控界面
- [ ] 实现应用配置界面

#### 阶段5: 测试和优化
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 性能优化
- [ ] 用户体验优化

### 9.2 关键技术点

#### 9.2.1 配置覆盖机制
实现应用在不同任务组中的配置覆盖：

```python
def get_effective_config(self, app_id: str, group_id: str) -> dict:
    """获取应用在特定任务组中的有效配置"""
    base_config = self.get_app_base_config(app_id)
    group_config = self.get_group_app_config(group_id, app_id)

    # 合并配置，组配置覆盖基础配置
    effective_config = {**base_config, **group_config.custom_config_overrides}
    return effective_config
```

#### 9.2.2 动态应用实例化
支持应用在不同任务组中的独立实例化：

```python
def create_app_instance(self, app_class: type, group_id: str) -> Application:
    """为特定任务组创建应用实例"""
    app = app_class(self.ctx)

    # 应用组特定的配置
    group_config = self.get_group_app_config(group_id, app.app_id)
    if group_config.custom_config_overrides:
        app.apply_config_overrides(group_config.custom_config_overrides)

    return app
```

#### 9.2.3 状态同步机制
确保UI界面能实时反映执行状态：

```python
class ExecutionStatusManager:
    """执行状态管理器"""

    def __init__(self):
        self.status_listeners: list[Callable] = []
        self.current_status: dict = {}

    def update_status(self, group_id: str, app_id: str, status: str) -> None:
        """更新执行状态"""
        self.current_status[f"{group_id}:{app_id}"] = status
        self.notify_listeners(group_id, app_id, status)

    def notify_listeners(self, group_id: str, app_id: str, status: str) -> None:
        """通知状态监听器"""
        for listener in self.status_listeners:
            listener(group_id, app_id, status)
```

## 10. 总结

### 10.1 架构优势

新的应用任务组架构具有以下优势：

1. **灵活性**: 用户可以根据需要创建多个任务组，每个任务组包含不同的应用组合
2. **可配置性**: 每个应用在不同任务组中可以有不同的配置，满足多样化需求
3. **智能调度**: 基于运行记录的智能执行决策，避免不必要的重复执行
4. **精确控制**: 支持复杂的执行频率、次数限制、冷却时间等控制策略
5. **可扩展性**: 架构设计支持未来功能的扩展，如并发执行、条件执行等
6. **向后兼容**: 现有代码无需修改即可正常运行
7. **用户友好**: 提供直观的UI界面，降低使用门槛

### 10.2 应用场景

该架构特别适用于以下场景：

1. **多账号管理**: 不同账号需要执行不同的应用组合
2. **时间段任务**: 不同时间段执行不同的任务组
3. **频率控制**: 同一应用在不同场景下有不同的执行频率需求
4. **资源管理**: 根据游戏资源(体力、次数等)智能调度任务执行
5. **条件执行**: 根据游戏状态或外部条件选择执行不同的任务组
6. **测试环境**: 为测试创建专门的任务组，避免影响正常使用

### 10.3 未来扩展

基于这个架构，未来可以考虑以下扩展：

1. **条件执行**: 支持基于条件的任务组执行
2. **并发执行**: 支持多个任务组并发执行
3. **定时执行**: 支持任务组的定时执行
4. **远程管理**: 支持通过网络接口管理任务组
5. **模板系统**: 支持任务组模板的导入导出

## 10. 总结

### 10.1 架构优势

新的应用任务组架构具有以下优势：

1. **简洁性**: 基于8个核心要点的清晰设计，易于理解和实现
2. **灵活性**: 用户可以创建多个任务组，每个任务组包含不同的应用组合
3. **可配置性**: 每个应用在不同任务组中可以有独立的配置，支持配置继承
4. **统一性**: 运行记录保持现有结构，避免复杂的状态管理
5. **向后兼容**: 现有代码无需修改即可正常运行
6. **可扩展性**: 架构设计支持未来功能的扩展
7. **用户友好**: 提供直观的配置和管理方式

### 10.2 核心特性

#### 10.2.1 应用注册机制
- 通过`OneDragonContext`统一管理应用创建函数
- 支持动态应用实例化
- 便于应用的插件化管理

#### 10.2.2 分层配置系统
- `group_id=one_dragon`作为默认配置
- 其他任务组可以继承并覆盖默认配置
- 配置文件组织清晰，易于管理

#### 10.2.3 统一运行记录
- 保持现有运行记录结构不变
- 避免重复执行，提高效率
- 简化状态管理复杂度

### 10.3 应用场景

该架构特别适用于以下场景：

1. **多账号管理**: 不同账号可以有不同的任务组配置
2. **场景化任务**: 如日常任务组、体力消耗组、周常任务组等
3. **配置差异化**: 同一应用在不同场景下有不同的配置需求
4. **测试环境**: 为测试创建专门的任务组，避免影响正常使用
5. **用户定制**: 用户可以根据个人需求创建自定义任务组

### 10.4 实现价值

1. **开发效率**: 清晰的架构设计降低开发复杂度
2. **维护性**: 模块化设计便于后续维护和扩展
3. **用户体验**: 灵活的任务组管理提升用户满意度
4. **系统稳定性**: 统一的运行记录避免状态冲突
5. **扩展性**: 为未来功能扩展奠定良好基础

### 10.5 与原方案的对比

相比于之前复杂的运行记录系统设计，当前方案具有以下优势：

1. **简化复杂度**: 运行记录保持现有结构，避免引入额外复杂性
2. **降低风险**: 向后兼容性更好，迁移风险更低
3. **实现成本**: 开发工作量更少，实现周期更短
4. **维护成本**: 系统复杂度降低，维护成本更低
5. **用户理解**: 概念更简单，用户更容易理解和使用

这个设计为游戏自动化项目提供了一个强大而简洁的任务管理框架，在保持系统简洁性的同时，为用户提供了足够的灵活性和控制力，将大大提升用户体验和系统的可维护性。