# 应用任务组(ApplicationGroup)设计文档

## 1. 项目概述

本文档描述了将当前的OneDragonApp升级为支持多任务组模式的设计方案。新的架构将允许用户创建多个任务组，每个任务组可以包含多个Application，每个Application都可以有独立的配置。

## 2. 当前架构分析

### 2.1 现有架构概述

当前项目采用三层架构：

```mermaid
graph TD
    A["OneDragonApp<br/>一条龙应用"] --> B["Application<br/>具体应用"]
    B --> C["Operation<br/>操作节点"]

    D["OneDragonConfig<br/>全局配置"] --> E["OneDragonAppConfig<br/>应用配置"]
    E --> F["各应用独立配置<br/>(CoffeeConfig等)"]

    G["OneDragonInstance<br/>实例管理"] --> H["多账号支持"]
```

### 2.2 核心组件分析

#### 2.2.1 OneDragonApp
- **职责**: 管理应用执行顺序和实例切换
- **核心功能**:
  - 获取应用列表(`get_app_list()`)
  - 按顺序执行应用(`get_one_dragon_apps_in_order()`)
  - 多实例支持和账号切换
- **限制**: 只支持单一的应用执行序列

#### 2.2.2 Application
- **职责**: 封装具体的业务逻辑
- **核心属性**:
  - `app_id`: 应用唯一标识
  - `run_record`: 运行记录
  - `need_notify`: 通知配置
- **配置**: 每个应用有独立的配置类

#### 2.2.3 配置系统
- **层次结构**:
  - 全局配置(`OneDragonConfig`)
  - 应用配置(`OneDragonAppConfig`)
  - 具体应用配置(如`CoffeeConfig`)
- **实例支持**: 通过`instance_idx`支持多实例配置

## 3. 新架构设计

1. `OneDragonContext` 新增应用注册方法，可以注册应用的创建函数
2. 新增 `OneDragonGroupApp`，类似于当前的 `OneDragonApp`，用于执行一个任务组。
3. `OneDragonGroupApp` 接受一个 `OneDragonGroupConfig`，里面只需要包含具体的应用ID，表明这个任务组需要运行哪些任务应用。
4. `OneDragonGroupApp` 按任务ID从 `OneDragonContext` 创建应用，并执行。
5. 当前 `OneDragonApp` 在后续改造为 `group_id=one_dragon` 的任务组，这也是默认的任务组，使用 `OneDragonGroupApp` 执行。
6. 每个应用的配置，将改成 `instace_idx` 和 `group_id` 下独立，但 `group_id=one_dragon` 为默认配置，其他 `group_id` 的应用未有配置时，使用默认配置。
7. 每个应用的运行记录，保持当前结构不变，即运行记录是账号下独立，不区分任务组。
