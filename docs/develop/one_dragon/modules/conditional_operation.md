# 条件操作模块设计文档

## 概述

条件操作模块（conditional_operation）是一个基于状态驱动的自动化操作框架，用于根据不同的状态条件执行相应的原子操作。该模块支持复杂的状态表达式、优先级管理、异步执行和场景切换等功能。

项目采用 `src-layout` 结构，源代码位于 `src/` 目录下。

## 核心架构

### 1. 核心组件关系图

```
ConditionalOperator (主控制器)
├── SceneHandler (场景处理器)
│   ├── StateHandler (状态处理器)
│   │   ├── StateCalTree (状态计算树)
│   │   │   └── StateRecorder (状态记录器)
│   │   └── AtomicOp (原子操作)
│   └── OperationTask (操作任务)
└── StateRecord (状态记录)
```

### 2. 模块组件说明

#### 2.1 ConditionalOperator (条件操作器)
- **职责**: 整个模块的主控制器，负责场景管理、状态监听和任务调度
- **核心功能**:
  - 管理触发场景和普通场景
  - 处理状态更新和场景触发
  - 控制任务的启动、停止和优先级管理
  - 支持异步执行和线程安全

#### 2.2 SceneHandler (场景处理器)
- **职责**: 管理特定场景下的状态处理器集合
- **核心功能**:
  - 包含多个状态处理器
  - 设置场景优先级和执行间隔
  - 根据触发时间获取符合条件的操作任务

#### 2.3 StateHandler (状态处理器)
- **职责**: 处理具体的状态判断和操作执行
- **核心功能**:
  - 包含状态计算树进行条件判断
  - 管理子处理器或原子操作
  - 支持打断状态设置
  - 提供调试名称显示

#### 2.4 StateCalTree (状态计算树)
- **职责**: 解析和计算复杂的状态表达式
- **核心功能**:
  - 支持逻辑运算符（AND、OR、NOT）
  - 支持括号优先级
  - 时间范围和数值范围判断
  - 递归计算状态表达式

#### 2.5 StateRecorder (状态记录器)
- **职责**: 记录和管理单个状态的历史信息
- **核心功能**:
  - 记录状态触发时间和数值
  - 支持状态互斥机制
  - 提供状态清除功能

#### 2.6 AtomicOp (原子操作)
- **职责**: 最小的执行单元，封装具体的操作逻辑
- **核心功能**:
  - 支持同步和异步执行
  - 提供停止和销毁机制
  - 可扩展的操作类型

#### 2.7 OperationTask (操作任务)
- **职责**: 管理一系列原子操作的执行
- **核心功能**:
  - 顺序执行原子操作列表
  - 支持任务中断和停止
  - 优先级管理
  - 异步执行支持

## 状态表达式语法

### 基本语法
- `[状态名, 时间最小值, 时间最大值]` - 基本状态判断
- `[状态名, 时间最小值, 时间最大值]{数值最小值, 数值最大值}` - 带数值范围的状态判断
- `&` - 逻辑与
- `|` - 逻辑或
- `!` - 逻辑非
- `()` - 括号优先级

### 示例
```
[闪避识别-黄光, 0, 1] | [闪避识别-红光, 0, 1]
([状态A, 0, 2] & [状态B, 1, 3]) | ![状态C, 0, 1]{0, 1}
```

## 配置结构

### 场景配置
```yaml
scenes:
  - triggers: ["状态名1", "状态名2"]  # 触发状态列表
    interval: 0.5                    # 执行间隔（秒）
    priority: 1                      # 场景优先级
    handlers:                        # 状态处理器列表
      - states: "[状态A, 0, 1]"      # 状态表达式
        debug_name: "处理器名称"      # 调试名称
        operations:                  # 操作列表
          - op_name: "操作名称"
            pre_delay: 0.1
            post_delay: 0.1
```

### 模板支持
- `state_template`: 状态处理器模板
- `operation_template`: 操作模板
- 支持模板嵌套和循环引用检测

## 执行流程

### 1. 初始化流程
1. 解析配置文件中的场景定义
2. 构建状态处理器和状态计算树
3. 注册状态记录器和原子操作
4. 验证状态表达式语法

### 2. 运行流程
1. 启动主循环（无触发场景）
2. 监听状态变化事件
3. 根据状态触发相应场景
4. 执行优先级判断和任务调度
5. 异步执行操作任务

### 3. 状态更新流程
1. 接收状态记录
2. 更新状态记录器
3. 处理互斥状态清除
4. 触发场景判断
5. 执行任务调度

### 4. 主循环执行流程
1. 启动后，`ConditionalOperator`会为普通场景（无触发状态）启动一个独立的主循环线程
2. 主循环线程持续检查普通场景下的状态处理器，按设定的时间间隔进行轮询
3. 当检测到符合条件的状态时，创建并执行对应的操作任务
4. 在两次状态检查之间，主循环会进入等待状态以节省系统资源

### 5. 场景触发流程
1. 当外部状态更新时，通过`update_state`或`batch_update_states`方法接收状态变更
2. 系统首先更新对应的状态记录器
3. 对于非清除状态，检查是否有关联的触发场景
4. 如果有触发场景且满足时间间隔要求，则获取符合条件的操作任务
5. 根据优先级判断是否可以打断当前运行的任务
6. 如果可以打断，则停止当前任务并执行新的操作任务

### 6. 主循环与触发场景切换逻辑
1. 系统通过`running_task_cnt`原子计数器协调主循环和触发场景的执行
2. 当有触发场景任务运行时，计数器增加，主循环会暂停状态检查，避免冲突
3. 当触发场景任务完成时，计数器减少，主循环恢复状态检查
4. 触发场景具有优先级管理机制，高优先级的触发场景可以打断低优先级任务
5. 优先级为`None`的任务可以被任意优先级的任务打断
6. 通过`_task_lock`锁机制确保任务切换的线程安全

## 线程安全设计

### 线程池管理
- `_od_conditional_op_executor`: 条件操作线程池（默认最大工作线程数）
- `_od_op_task_executor`: 操作任务线程池（默认最大工作线程数）

### 锁机制
- `_task_lock`: 任务锁，保护运行状态和任务切换
- `_op_lock`: 操作锁，保护当前执行的原子操作

### 原子计数器
- `running_task_cnt`: 运行任务计数器，用于协调主循环和触发场景

## 优先级管理

### 场景优先级
- 数值越大优先级越高
- `None` 表示可被任意打断
- 高优先级场景可以打断低优先级场景

### 打断机制
- 支持基于状态的任务打断
- 支持基于优先级的场景切换
- 提供优雅的停止机制

## 扩展点

### 1. 自定义原子操作
继承 `AtomicOp` 类实现具体的操作逻辑：
```python
class CustomOp(AtomicOp):
    def execute(self):
        # 实现具体操作
        pass
```

### 2. 自定义状态获取
实现 `get_state_recorder` 方法：
```python
def get_state_recorder(self, state_name: str) -> Optional[StateRecorder]:
    # 返回对应的状态记录器
    pass
```

## 调试支持

### 调试信息
- 支持 `debug_name` 字段用于调试显示，在配置中设置后会在日志中显示更有意义的名称
- 提供表达式显示和优先级显示
- 支持触发场景显示

### 日志记录
- 详细的状态触发日志
- 任务执行状态记录
- 错误异常捕获和记录

## 注意事项

1. **状态表达式语法**: 必须严格按照语法规则编写，支持语法验证
2. **循环引用**: 模板引用时会检测循环引用，避免无限递归
3. **线程安全**: 所有状态更新和任务调度都是线程安全的
4. **资源管理**: 提供完整的销毁机制，避免内存泄漏
5. **异常处理**: 操作执行异常不会影响整体框架运行

## 使用示例

```python
# 创建条件操作器
operator = ConditionalOperator("config_dir", "template_name")

# 初始化
operator.init(op_getter, scene_handler_getter, operation_template_getter)

# 启动运行
operator.start_running_async()

# 更新状态
state_record = StateRecord("状态名", time.time(), value=1)
operator.update_state(state_record)

# 停止运行
operator.stop_running()

# 销毁
operator.dispose()
```

这个模块为复杂的自动化场景提供了强大而灵活的状态驱动执行框架，支持高并发、优先级管理和复杂的条件判断逻辑。